# Football Matches Display

A clean, responsive web application that fetches and displays football matches from FawaNews with real-time status updates and interactive cards.

## 🏗️ Project Structure

```
FawaNews/
├── index.html              # Main HTML file
├── assets/
│   ├── css/
│   │   └── styles.css      # All CSS styles
│   └── js/
│       └── script.js       # All JavaScript functionality
├── html_element_sample.html # Sample HTML structure for reference
└── README.md               # Project documentation
```

## ✨ Features

- **Live Match Updates**: Real-time status with live indicators
- **Smart Sorting**: Live matches appear first, followed by upcoming and finished matches
- **Interactive Cards**: Hover effects with clickable actions based on match status
- **Responsive Design**: Works on desktop and mobile devices
- **Professional UI**: Clean, modern design with smooth animations
- **CORS Handling**: Multiple fallback methods to fetch data

## 🎯 Match Status System

### Status Priority (Top to Bottom):
1. **🔴 LIVE** - Currently playing matches (clickable)
2. **🟡 COMING SOON** - About to start (not clickable)
3. **🟢 NOT STARTED** - Upcoming matches (not clickable)
4. **⚫ FINISHED** - Completed matches (not clickable)

### Clickability Rules:
- **Clickable**: Only matches with "live" or "Watch The Match" hover text
- **Non-clickable**: All other matches (finished, not started, coming soon)

## 🚀 Getting Started

1. **Clone or download** the project files
2. **Open `index.html`** in a web browser
3. **Wait for automatic loading** - matches will appear automatically
4. **Click on live matches** to view match details

## 📁 File Details

### `index.html`
- Clean HTML structure
- Links to external CSS and JS files
- Loading indicator and data containers

### `assets/css/styles.css`
- Complete styling for all components
- Responsive design rules
- Animation keyframes
- Status-based color schemes

### `assets/js/script.js`
- Data fetching and parsing logic
- Match card generation
- CORS proxy handling
- Interactive functionality
- Sorting and filtering

## 🔧 Configuration

### Target Website
- **URL**: `https://fawanews.co.uk/`
- **Selector**: `#content > div > div > div > div.nv-content-wrap.entry-content > div.AlbaSportFixture.asp-font`

### CORS Proxies
The application uses multiple proxy services as fallbacks:
- `https://api.allorigins.win/get?url=`
- `https://cors-anywhere.herokuapp.com/`
- `https://api.codetabs.com/v1/proxy?quest=`

### Hover Text Mapping
```javascript
{
    "Soon": "Comming soon",
    "Live": "live", 
    "Show": "Watch The Match",
    "NotStarted": "Not started",
    "FinMatch": "Match has finished",
    "Finished": "Finished"
}
```

## 🎨 Customization

### Colors
- **Live**: Red (`#dc3545`)
- **Coming Soon**: Yellow (`#ffc107`)
- **Not Started**: Green (`#28a745`)
- **Finished**: Gray (`#6c757d`)

### Animations
- **Pulse effect** for live indicators
- **Hover animations** for cards
- **Smooth transitions** (0.3s ease)

## 🌐 Browser Support

- **Modern browsers** with ES6+ support
- **Chrome, Firefox, Safari, Edge**
- **Mobile browsers** (responsive design)

## 📱 Responsive Breakpoints

- **Desktop**: Full layout with side-by-side teams
- **Mobile** (`max-width: 768px`): Stacked layout for better mobile experience

## 🔍 Troubleshooting

### No Matches Loading
1. Check internet connection
2. Try refreshing the page
3. Check browser console for errors

### CORS Issues
- The app automatically tries multiple proxy services
- If all fail, an error message will be displayed

### Styling Issues
- Ensure `assets/css/styles.css` is accessible
- Check browser developer tools for CSS errors

## 📄 License

This project is for educational and personal use.

## 🤝 Contributing

Feel free to submit issues and enhancement requests!
