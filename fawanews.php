<?php
/**
 * Plugin Name: FawaNews Sports
 * Plugin URI: https://fawanews.com
 * Description: Display live sports matches with professional cards and real-time updates. Use shortcode [MATCH_SCORES] to display matches anywhere.
 * Version: 1.0.2
 * Author: FawaNews
 * Author URI: https://fawanews.com
 * License: GPL v2 or later
 * Text Domain: fawanews
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('FAWANEWS_VERSION', '1.0.2');
define('FAWANEWS_PLUGIN_URL', plugin_dir_url(__FILE__));
define('FAWANEWS_PLUGIN_PATH', plugin_dir_path(__FILE__));

class FawaNews_Plugin {
    
    public function __construct() {
        add_action('init', array($this, 'init'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_shortcode('MATCH_SCORES', array($this, 'match_scores_shortcode'));
        add_action('wp_ajax_fawanews_get_matches', array($this, 'ajax_get_matches'));
        add_action('wp_ajax_nopriv_fawanews_get_matches', array($this, 'ajax_get_matches'));
        add_action('wp_ajax_fawanews_extract_iframe', array($this, 'ajax_extract_iframe'));
        add_action('wp_ajax_nopriv_fawanews_extract_iframe', array($this, 'ajax_extract_iframe'));
    }
    
    public function init() {
        // Plugin initialization
        load_plugin_textdomain('fawanews', false, dirname(plugin_basename(__FILE__)) . '/languages');
    }
    
    public function enqueue_scripts() {
        // Always enqueue scripts to ensure they're available
        wp_enqueue_style(
            'fawanews-styles',
            FAWANEWS_PLUGIN_URL . 'assets/css/styles.css',
            array(),
            FAWANEWS_VERSION
        );

        wp_enqueue_script(
            'fawanews-script',
            FAWANEWS_PLUGIN_URL . 'assets/js/plugin-script.js',
            array('jquery'),
            FAWANEWS_VERSION,
            true
        );

        // Localize script for AJAX
        wp_localize_script('fawanews-script', 'fawanews_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('fawanews_nonce')
        ));
    }
    
    public function match_scores_shortcode($atts) {
        $atts = shortcode_atts(array(
            'default_tab' => 'today'
        ), $atts, 'MATCH_SCORES');
        
        // Force enqueue scripts if not already done
        wp_enqueue_style('fawanews-styles');
        wp_enqueue_script('fawanews-script');

        ob_start();
        ?>
        <div class="fawanews-container" data-plugin-version="<?php echo FAWANEWS_VERSION; ?>">
            <div class="loading-info" id="fawanewsLoadingInfo">
                <div class="loader-spinner"></div>
                <p>Loading matches...</p>
            </div>

            <div class="tab-bar" id="fawanewsTabBar" style="display: none;">
                <div class="tab-item" data-filter="yesterday">
                    <span class="tab-text">Yesterday</span>
                </div>
                <div class="tab-item active" data-filter="today">
                    <span class="tab-text">Today</span>
                </div>
                <div class="tab-item" data-filter="tomorrow">
                    <span class="tab-text">Tomorrow</span>
                </div>
            </div>

            <div id="fawanewsStatus"></div>
            <div id="fawanewsDataContainer" class="data-container" style="display: none;">
                <div id="fawanewsDataContent" class="data-content"></div>
            </div>
        </div>

        <script type="text/javascript">
        // Debug information
        console.log('FawaNews Sports Plugin v<?php echo FAWANEWS_VERSION; ?> loaded');
        console.log('AJAX URL:', '<?php echo admin_url('admin-ajax.php'); ?>');
        console.log('jQuery loaded:', typeof jQuery !== 'undefined');

        // Create AJAX object if it doesn't exist
        if (typeof fawanews_ajax === 'undefined') {
            window.fawanews_ajax = {
                ajax_url: '<?php echo admin_url('admin-ajax.php'); ?>',
                nonce: '<?php echo wp_create_nonce('fawanews_nonce'); ?>'
            };
            console.log('FawaNews: Created AJAX object manually');
        }

        // Ensure script runs even if jQuery is loaded later
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, checking for fawanews_ajax object:', typeof fawanews_ajax !== 'undefined');

            // Try to initialize the plugin
            if (typeof initFawaNewsPlugin === 'function') {
                console.log('FawaNews: Calling initFawaNewsPlugin from shortcode');
                initFawaNewsPlugin();
            } else {
                console.log('FawaNews: initFawaNewsPlugin not available, waiting for script...');
                // Wait for script to load and try again
                setTimeout(function() {
                    if (typeof initFawaNewsPlugin === 'function') {
                        console.log('FawaNews: Delayed call to initFawaNewsPlugin');
                        initFawaNewsPlugin();
                    } else {
                        console.error('FawaNews: Plugin script not loaded properly');
                    }
                }, 1000);
            }
        });
        </script>
        <?php
        return ob_get_clean();
    }
    
    public function ajax_get_matches() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'fawanews_nonce')) {
            wp_die('Security check failed');
        }

        $filter = sanitize_text_field($_POST['filter'] ?? 'today');

        // Include the match processor
        require_once FAWANEWS_PLUGIN_PATH . 'includes/class-match-processor.php';

        $processor = new FawaNews_Match_Processor();
        $result = $processor->fetchAndProcessMatches($filter);

        wp_send_json($result);
    }
    
    public function ajax_extract_iframe() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'fawanews_nonce')) {
            wp_die('Security check failed');
        }

        $url = esc_url_raw($_POST['url'] ?? '');

        if (empty($url)) {
            wp_send_json(array(
                'success' => false,
                'iframeUrl' => null,
                'error' => 'URL parameter is required'
            ));
        }

        // Include the iframe extractor
        require_once FAWANEWS_PLUGIN_PATH . 'includes/class-iframe-extractor.php';

        $extractor = new FawaNews_Iframe_Extractor();
        $result = $extractor->extractIframeUrl($url);

        wp_send_json($result);
    }
}

// Initialize the plugin
new FawaNews_Plugin();

// Activation hook
register_activation_hook(__FILE__, 'fawanews_activate');
function fawanews_activate() {
    // Create any necessary database tables or options
    update_option('fawanews_version', FAWANEWS_VERSION);
}

// Deactivation hook
register_deactivation_hook(__FILE__, 'fawanews_deactivate');
function fawanews_deactivate() {
    // Clean up if necessary
}

// Uninstall hook
register_uninstall_hook(__FILE__, 'fawanews_uninstall');
function fawanews_uninstall() {
    // Remove options and clean up
    delete_option('fawanews_version');
}
