/* Base Styles */
body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #ffffff;
}

.container {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.data-container {
    margin-top: 20px;
}

.data-content {
    word-wrap: break-word;
    font-family: Arial, sans-serif;
    font-size: 14px;
    line-height: 1.4;
}

/* Loading Styles */
.loading-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;
}

.loader-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

.loading-info p {
    color: #666;
    font-size: 16px;
    margin: 0;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error and No Data Styles */
.error-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;
    color: #666;
}

.error-icon {
    font-size: 48px;
    margin-bottom: 20px;
    color: #dc3545;
}

.error-title {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
}

.error-description {
    font-size: 16px;
    color: #666;
    max-width: 400px;
    line-height: 1.5;
}

.no-matches {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;
    color: #666;
}

.no-matches-icon {
    font-size: 48px;
    margin-bottom: 20px;
    color: #6c757d;
}

.no-matches-title {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
}

.no-matches-description {
    font-size: 16px;
    color: #666;
}

/* Match Cards Styles */
.matches-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
    padding: 0;
    margin: 0;
}

.match-card {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    padding: 20px;
    border: 1px solid #f0f0f0;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.match-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #ddd, #ddd);
    transition: background 0.3s ease;
}

.match-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: #e0e0e0;
}

.match-card:hover::before {
    background: linear-gradient(90deg, #007bff, #0056b3);
}

/* Status-based card styling */
.match-card.status-live::before {
    background: linear-gradient(90deg, #dc3545, #c82333) !important;
}

.match-card.status-finished::before {
    background: linear-gradient(90deg, #6c757d, #545b62) !important;
}

.match-card.status-coming-soon::before {
    background: linear-gradient(90deg, #ffc107, #e0a800) !important;
}

.match-card.status-not-started::before {
    background: linear-gradient(90deg, #28a745, #1e7e34) !important;
}

/* Match Overlay */
.match-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 123, 255, 0.95);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 10;
}

.match-card:hover .match-overlay {
    opacity: 1;
}

.overlay-text {
    color: white;
    font-size: 18px;
    font-weight: bold;
    text-align: center;
    padding: 10px;
}

.status-live .match-overlay {
    background: rgba(220, 53, 69, 0.95);
}

.status-finished .match-overlay {
    background: rgba(108, 117, 125, 0.95);
}

.status-coming-soon .match-overlay {
    background: rgba(255, 193, 7, 0.95);
}

.status-not-started .match-overlay {
    background: rgba(40, 167, 69, 0.95);
}

/* Match Header */
.match-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.match-time {
    text-align: right;
}

.scheduled-time {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.timezone {
    font-size: 11px;
    color: #888;
    margin-top: 2px;
}

/* Match Teams */
.match-teams {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    padding: 15px 0;
}

.team {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    max-width: 35%;
}

.team-logo {
    width: 70px;
    height: 70px;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid #e9ecef;
    transition: transform 0.3s ease;
}

.match-card:hover .team-logo {
    transform: scale(1.05);
}

.team-logo img {
    width: 55px;
    height: 55px;
    object-fit: contain;
}

.team-name {
    font-weight: 600;
    font-size: 15px;
    color: #333;
    text-align: center;
    margin-bottom: 8px;
    line-height: 1.3;
}

.team-score {
    font-size: 24px;
    font-weight: bold;
    color: #007bff;
    background: #f0f8ff;
    padding: 8px 16px;
    border-radius: 8px;
    min-width: 45px;
    text-align: center;
    border: 2px solid #e3f2fd;
}

/* Match Center Status */
.match-center {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 0 0 auto;
    margin: 0 20px;
    min-width: 120px;
}

.match-status-center {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 12px;
    border-radius: 12px;
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    min-height: 80px;
    justify-content: center;
}

.status-badge {
    display: flex;
    align-items: center;
    gap: 6px;
    font-weight: bold;
    font-size: 12px;
    padding: 4px 12px;
    border-radius: 20px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
}

/* Status Badge Colors */
.status-live .status-badge {
    background: #dc3545;
    color: white;
}

.status-live .status-indicator {
    background: white;
    animation: pulse 1.5s infinite;
}

.status-finished .status-badge {
    background: #6c757d;
    color: white;
}

.status-finished .status-indicator {
    background: white;
}

.status-coming-soon .status-badge {
    background: #ffc107;
    color: #333;
}

.status-coming-soon .status-indicator {
    background: #333;
}

.status-not-started .status-badge {
    background: #28a745;
    color: white;
}

.status-not-started .status-indicator {
    background: white;
}

/* Timer and Status Indicators */
.countdown-timer {
    font-family: 'Courier New', monospace;
    font-size: 16px;
    font-weight: bold;
    color: #333;
    background: #fff;
    padding: 6px 12px;
    border-radius: 8px;
    border: 1px solid #ddd;
    margin-top: 4px;
    min-width: 80px;
    text-align: center;
}

.live-indicator {
    font-family: Arial, sans-serif;
    font-size: 14px;
    font-weight: bold;
    color: #fff;
    background: #dc3545;
    padding: 6px 12px;
    border-radius: 8px;
    margin-top: 4px;
    text-align: center;
    animation: pulse 1.5s infinite;
    text-transform: uppercase;
}

.finished-indicator {
    font-family: Arial, sans-serif;
    font-size: 14px;
    font-weight: bold;
    color: #fff;
    background: #6c757d;
    padding: 6px 12px;
    border-radius: 8px;
    margin-top: 4px;
    text-align: center;
    text-transform: uppercase;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Match Info */
.match-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 12px;
    border-top: 1px solid #eee;
    margin-bottom: 12px;
}

.competition {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: #495057;
    font-size: 14px;
}

.broadcast-info {
    display: flex;
    gap: 20px;
}

.commentary, .tv {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    color: #6c757d;
}

/* Simple icons using CSS */
.icon-trophy::before { content: "🏆"; margin-right: 4px; }
.icon-mic::before { content: "🎤"; margin-right: 4px; }
.icon-tv::before { content: "📺"; margin-right: 4px; }

/* Responsive design */
@media (max-width: 768px) {
    .match-teams {
        flex-direction: column;
        gap: 15px;
    }
    
    .team {
        max-width: 100%;
        flex-direction: row;
        justify-content: space-between;
        width: 100%;
        padding: 10px;
        background: #f8f9fa;
        border-radius: 8px;
    }
    
    .team-logo {
        width: 40px;
        height: 40px;
        margin-bottom: 0;
    }
    
    .team-logo img {
        width: 35px;
        height: 35px;
    }
    
    .team-name {
        margin-bottom: 0;
        flex: 1;
        text-align: left;
        margin-left: 10px;
    }
    
    .match-center {
        margin: 10px 0;
    }
    
    .match-info {
        flex-direction: column;
        gap: 10px;
        align-items: flex-start;
    }
    
    .broadcast-info {
        flex-direction: column;
        gap: 5px;
    }
}
