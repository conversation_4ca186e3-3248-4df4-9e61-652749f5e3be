/* Base Styles */
body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #ffffff;
}

.container,
.mubashardev-container {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.data-container {
    margin-top: 20px;
}

.data-content {
    word-wrap: break-word;
    font-family: Arial, sans-serif;
    font-size: 14px;
    line-height: 1.4;
}

/* Tab Bar Styles */
.tab-bar {
    display: flex;
    background: #ffffff;
    border-radius: 14px;
    padding: 8px;
    margin-bottom: 28px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
    border: 1px solid #d1d5db;
}

.tab-item {
    flex: 1;
    text-align: center;
    padding: 12px 20px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    font-size: 16px;
    position: relative;
}

.tab-item:hover {
    background: rgba(255, 99, 68, 0.08);
    color: #FF6344;
}

.tab-item.active {
    background: #FF6344;
    color: white;
    box-shadow: 0 3px 10px rgba(255, 99, 68, 0.3);
}

.tab-item.active:hover {
    background: #e55039;
    color: white;
}

.tab-text {
    display: block;
    position: relative;
}

/* Loading Styles */
.loading-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;
}

.loader-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

.loading-info p {
    color: #666;
    font-size: 16px;
    margin: 0;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* Error and No Data Styles */
.error-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;
    color: #666;
}

.error-icon {
    font-size: 48px;
    margin-bottom: 20px;
    color: #dc3545;
}

.error-title {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
}

.error-description {
    font-size: 16px;
    color: #666;
    max-width: 400px;
    line-height: 1.5;
}

.no-matches {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;
    color: #666;
}

.no-matches-icon {
    font-size: 48px;
    margin-bottom: 20px;
    color: #6c757d;
}

.no-matches-title {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
}

.no-matches-description {
    font-size: 16px;
    color: #666;
}

/* Match Cards Styles */
.matches-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
    padding: 0;
    margin: 0;
}

.match-card {
    background: #ffffff;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    padding: 24px;
    border: 1px solid #d1d5db;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.match-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #ddd, #ddd);
    transition: background 0.3s ease;
}

.match-card:hover {
    transform: translateY(-6px);
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.18);
    border-color: #d1d5db;
}

.match-card:hover::before {
    background: linear-gradient(90deg, #FF6344, #e55039);
}

/* Status-based card styling */
.match-card.status-live::before {
    background: linear-gradient(90deg, #ef4444, #dc2626) !important;
}

.match-card.status-finished::before {
    background: linear-gradient(90deg, #64748b, #475569) !important;
}

.match-card.status-coming-soon::before {
    background: linear-gradient(90deg, #f59e0b, #d97706) !important;
}

.match-card.status-not-started::before {
    background: linear-gradient(90deg, #10b981, #059669) !important;
}

/* Match Overlay */
.match-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 123, 255, 0.95);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 10;
}

.match-card:hover .match-overlay {
    opacity: 1;
}

.overlay-text {
    color: white;
    font-size: 18px;
    font-weight: bold;
    text-align: center;
    padding: 10px;
}

.status-live .match-overlay {
    background: rgba(220, 53, 69, 0.95);
}

.status-finished .match-overlay {
    background: rgba(108, 117, 125, 0.95);
}

.status-coming-soon .match-overlay {
    background: rgba(255, 193, 7, 0.95);
}

.status-not-started .match-overlay {
    background: rgba(40, 167, 69, 0.95);
}

/* Match Header */
.match-header {
    display: none;
}

/* Match Teams */
.match-teams {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    padding: 15px 0;
}

.team {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    max-width: 35%;
    position: relative;
}

.team-logo {
    width: 120px;
    height: 120px;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #ffffff;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid #d1d5db;
    transition: transform 0.3s ease;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.12);
}

.match-card:hover .team-logo {
    transform: scale(1.05);
}

.team-logo img {
    width: 100px;
    height: 100px;
    object-fit: contain;
}

.team-name {
    font-weight: 600;
    font-size: 15px;
    color: #111827;
    text-align: center;
    margin-bottom: 8px;
    line-height: 1.3;
}

.team-score {
    display: none;
}

.home-team .team-score {
    right: -30px;
}

.away-team .team-score {
    left: -30px;
}

/* Match Center Status */
.match-center {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 0 0 auto;
    margin: 0 20px;
    min-width: 140px;
}

.match-time-center {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    padding: 18px;
    border-radius: 16px;
    background: #ffffff;
    border: 2px solid #d1d5db;
    min-height: 100px;
    justify-content: center;
    box-shadow: 0 3px 12px rgba(0, 0, 0, 0.1);
}

.center-time {
    font-size: 24px;
    font-weight: bold;
    color: #111827;
    text-align: center;
    line-height: 1.2;
}

.center-status {
    font-size: 14px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    text-align: center;
    padding: 4px 12px;
    border-radius: 12px;
}

.center-status.live {
    background: #ef4444;
    color: white;
    animation: pulse 1.5s infinite;
    box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
}

.center-status.upcoming {
    background: #10b981;
    color: white;
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.center-status.finished {
    background: #64748b;
    color: white;
    box-shadow: 0 2px 8px rgba(100, 116, 139, 0.3);
}



/* Timer and Status Indicators */
.countdown-timer {
    font-family: 'Courier New', monospace;
    font-size: 16px;
    font-weight: bold;
    color: #333;
    background: #fff;
    padding: 6px 12px;
    border-radius: 8px;
    border: 1px solid #ddd;
    margin-top: 4px;
    min-width: 80px;
    text-align: center;
}

.live-indicator {
    font-family: Arial, sans-serif;
    font-size: 14px;
    font-weight: bold;
    color: #fff;
    background: #dc3545;
    padding: 6px 12px;
    border-radius: 8px;
    margin-top: 4px;
    text-align: center;
    animation: pulse 1.5s infinite;
    text-transform: uppercase;
}

.finished-indicator {
    font-family: Arial, sans-serif;
    font-size: 14px;
    font-weight: bold;
    color: #fff;
    background: #6c757d;
    padding: 6px 12px;
    border-radius: 8px;
    margin-top: 4px;
    text-align: center;
    text-transform: uppercase;
}

@keyframes pulse {
    0% {
        opacity: 1;
    }

    50% {
        opacity: 0.5;
    }

    100% {
        opacity: 1;
    }
}

/* Match Info */
.match-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 12px;
    border-top: 1px solid #eee;
    margin-bottom: 12px;
}

.competition {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: #374151;
    font-size: 14px;
}

.broadcast-info {
    display: flex;
    gap: 20px;
}

.commentary,
.tv {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    color: #6b7280;
}

/* Simple icons using CSS */
.icon-trophy::before {
    content: "🏆";
    margin-right: 4px;
}

.icon-mic::before {
    content: "🎤";
    margin-right: 4px;
}

.icon-tv::before {
    content: "📺";
    margin-right: 4px;
}

/* Card Loader Overlay */
.card-loader-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 123, 255, 0.95);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 20;
    border-radius: 12px;
}

.card-loader-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: white;
    text-align: center;
}

.card-loader-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

.card-loader-content p {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

/* Try Again Overlay */
.try-again-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 193, 7, 0.95);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 20;
    border-radius: 12px;
}

.try-again-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #333;
    text-align: center;
}

.try-again-icon {
    font-size: 48px;
    margin-bottom: 10px;
}

.try-again-content p {
    margin: 0 0 5px 0;
    font-size: 18px;
    font-weight: bold;
}

.try-again-content small {
    font-size: 14px;
    opacity: 0.8;
}

/* Responsive Design - Maintaining Same Layout */
.container {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

/* Large screens (1200px+) */
@media (min-width: 1200px) {
    .match-card {
        padding: 28px;
    }

    .team-logo {
        width: 140px;
        height: 140px;
    }

    .team-logo img {
        width: 120px;
        height: 120px;
    }

    .team-score {
        font-size: 36px;
        padding: 14px 22px;
        min-width: 70px;
    }

    .center-time {
        font-size: 28px;
    }
}

/* Medium screens (768px - 1199px) */
@media (min-width: 768px) and (max-width: 1199px) {
    .container {
        padding: 16px;
    }

    .match-card {
        padding: 20px;
    }

    .team-logo {
        width: 100px;
        height: 100px;
    }

    .team-logo img {
        width: 85px;
        height: 85px;
    }

    .team-score {
        font-size: 28px;
        padding: 10px 16px;
        min-width: 50px;
        right: -25px;
    }

    .away-team .team-score {
        left: -25px;
    }

    .match-center {
        min-width: 120px;
        margin: 0 15px;
    }

    .match-time-center {
        padding: 15px;
        min-height: 90px;
    }

    .center-time {
        font-size: 22px;
    }

    .team-name {
        font-size: 14px;
    }
}

/* Small screens (480px - 767px) */
@media (min-width: 480px) and (max-width: 767px) {
    .container {
        padding: 12px;
    }

    .match-card {
        padding: 16px;
    }

    .team-logo {
        width: 80px;
        height: 80px;
    }

    .team-logo img {
        width: 68px;
        height: 68px;
    }

    .team-score {
        font-size: 24px;
        padding: 8px 14px;
        min-width: 45px;
        right: -20px;
    }

    .away-team .team-score {
        left: -20px;
    }

    .match-center {
        min-width: 100px;
        margin: 0 12px;
    }

    .match-time-center {
        padding: 12px;
        min-height: 80px;
    }

    .center-time {
        font-size: 18px;
    }

    .center-status {
        font-size: 12px;
        padding: 3px 10px;
    }

    .team-name {
        font-size: 13px;
    }

    .tab-item {
        padding: 10px 16px;
        font-size: 14px;
    }

    .match-info {
        padding-top: 10px;
        margin-bottom: 10px;
    }

    .competition {
        font-size: 13px;
    }

    .commentary,
    .tv {
        font-size: 11px;
    }
}

/* Extra small screens (320px - 479px) */
@media (max-width: 479px) {
    .container {
        padding: 8px;
    }

    .match-card {
        padding: 12px;
    }

    .team-logo {
        width: 70px;
        height: 70px;
    }

    .team-logo img {
        width: 58px;
        height: 58px;
    }

    .team-score {
        font-size: 20px;
        padding: 6px 12px;
        min-width: 40px;
        right: -15px;
    }

    .away-team .team-score {
        left: -15px;
    }

    .match-center {
        min-width: 90px;
        margin: 0 10px;
    }

    .match-time-center {
        padding: 10px;
        min-height: 70px;
    }

    .center-time {
        font-size: 16px;
    }

    .center-status {
        font-size: 11px;
        padding: 2px 8px;
    }

    .team-name {
        font-size: 12px;
    }

    .tab-item {
        padding: 8px 12px;
        font-size: 13px;
    }

    .tab-bar {
        padding: 6px;
        margin-bottom: 20px;
    }

    .match-info {
        padding-top: 8px;
        margin-bottom: 8px;
    }

    .competition {
        font-size: 12px;
    }

    .commentary,
    .tv {
        font-size: 10px;
    }

    .broadcast-info {
        gap: 15px;
    }
}