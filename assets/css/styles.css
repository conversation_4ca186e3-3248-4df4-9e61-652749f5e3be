/* Base Styles */
body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #ffffff;
}

.container {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.data-container {
    margin-top: 20px;
}

.data-content {
    word-wrap: break-word;
    font-family: Arial, sans-serif;
    font-size: 14px;
    line-height: 1.4;
}

/* Loading Styles */
.loading-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;
}

.loader-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

.loading-info p {
    color: #666;
    font-size: 16px;
    margin: 0;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* Error and No Data Styles */
.error-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;
    color: #666;
}

.error-icon {
    font-size: 48px;
    margin-bottom: 20px;
    color: #dc3545;
}

.error-title {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
}

.error-description {
    font-size: 16px;
    color: #666;
    max-width: 400px;
    line-height: 1.5;
}

.no-matches {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;
    color: #666;
}

.no-matches-icon {
    font-size: 48px;
    margin-bottom: 20px;
    color: #6c757d;
}

.no-matches-title {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
}

.no-matches-description {
    font-size: 16px;
    color: #666;
}

/* Match Cards Styles */
.matches-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
    padding: 0;
    margin: 0;
}

.match-card {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    padding: 20px;
    border: 1px solid #f0f0f0;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.match-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #ddd, #ddd);
    transition: background 0.3s ease;
}

.match-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: #e0e0e0;
}

.match-card:hover::before {
    background: linear-gradient(90deg, #007bff, #0056b3);
}

/* Status-based card styling */
.match-card.status-live::before {
    background: linear-gradient(90deg, #dc3545, #c82333) !important;
}

.match-card.status-finished::before {
    background: linear-gradient(90deg, #6c757d, #545b62) !important;
}

.match-card.status-coming-soon::before {
    background: linear-gradient(90deg, #ffc107, #e0a800) !important;
}

.match-card.status-not-started::before {
    background: linear-gradient(90deg, #28a745, #1e7e34) !important;
}

/* Match Overlay */
.match-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 123, 255, 0.95);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 10;
}

.match-card:hover .match-overlay {
    opacity: 1;
}

.overlay-text {
    color: white;
    font-size: 18px;
    font-weight: bold;
    text-align: center;
    padding: 10px;
}

.status-live .match-overlay {
    background: rgba(220, 53, 69, 0.95);
}

.status-finished .match-overlay {
    background: rgba(108, 117, 125, 0.95);
}

.status-coming-soon .match-overlay {
    background: rgba(255, 193, 7, 0.95);
}

.status-not-started .match-overlay {
    background: rgba(40, 167, 69, 0.95);
}

/* Match Header */
.match-header {
    display: none;
}

/* Match Teams */
.match-teams {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    padding: 15px 0;
}

.team {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    max-width: 35%;
    position: relative;
    padding-top: 50px;
}

.team-logo {
    width: 120px;
    height: 120px;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    border-radius: 50%;
    overflow: hidden;
    border: 4px solid #e9ecef;
    transition: transform 0.3s ease;
}

.match-card:hover .team-logo {
    transform: scale(1.05);
}

.team-logo img {
    width: 100px;
    height: 100px;
    object-fit: contain;
}

.team-name {
    font-weight: 600;
    font-size: 15px;
    color: #333;
    text-align: center;
    margin-bottom: 8px;
    line-height: 1.3;
}

.team-score {
    font-size: 28px;
    font-weight: bold;
    color: #007bff;
    background: #f0f8ff;
    padding: 10px 18px;
    border-radius: 10px;
    min-width: 50px;
    text-align: center;
    border: 2px solid #e3f2fd;
    position: absolute;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
}

/* Match Center Status */
.match-center {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 0 0 auto;
    margin: 0 20px;
    min-width: 140px;
}

.match-time-center {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    padding: 15px;
    border-radius: 15px;
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    min-height: 100px;
    justify-content: center;
}

.center-time {
    font-size: 24px;
    font-weight: bold;
    color: #333;
    text-align: center;
    line-height: 1.2;
}

.center-status {
    font-size: 14px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    text-align: center;
    padding: 4px 12px;
    border-radius: 12px;
}

.center-status.live {
    background: #dc3545;
    color: white;
    animation: pulse 1.5s infinite;
}

.center-status.upcoming {
    background: #28a745;
    color: white;
}

.center-status.finished {
    background: #6c757d;
    color: white;
}



/* Timer and Status Indicators */
.countdown-timer {
    font-family: 'Courier New', monospace;
    font-size: 16px;
    font-weight: bold;
    color: #333;
    background: #fff;
    padding: 6px 12px;
    border-radius: 8px;
    border: 1px solid #ddd;
    margin-top: 4px;
    min-width: 80px;
    text-align: center;
}

.live-indicator {
    font-family: Arial, sans-serif;
    font-size: 14px;
    font-weight: bold;
    color: #fff;
    background: #dc3545;
    padding: 6px 12px;
    border-radius: 8px;
    margin-top: 4px;
    text-align: center;
    animation: pulse 1.5s infinite;
    text-transform: uppercase;
}

.finished-indicator {
    font-family: Arial, sans-serif;
    font-size: 14px;
    font-weight: bold;
    color: #fff;
    background: #6c757d;
    padding: 6px 12px;
    border-radius: 8px;
    margin-top: 4px;
    text-align: center;
    text-transform: uppercase;
}

@keyframes pulse {
    0% {
        opacity: 1;
    }

    50% {
        opacity: 0.5;
    }

    100% {
        opacity: 1;
    }
}

/* Match Info */
.match-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 12px;
    border-top: 1px solid #eee;
    margin-bottom: 12px;
}

.competition {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: #495057;
    font-size: 14px;
}

.broadcast-info {
    display: flex;
    gap: 20px;
}

.commentary,
.tv {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    color: #6c757d;
}

/* Simple icons using CSS */
.icon-trophy::before {
    content: "🏆";
    margin-right: 4px;
}

.icon-mic::before {
    content: "🎤";
    margin-right: 4px;
}

.icon-tv::before {
    content: "📺";
    margin-right: 4px;
}

/* Card Loader Overlay */
.card-loader-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 123, 255, 0.95);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 20;
    border-radius: 12px;
}

.card-loader-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: white;
    text-align: center;
}

.card-loader-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

.card-loader-content p {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

/* Try Again Overlay */
.try-again-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 193, 7, 0.95);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 20;
    border-radius: 12px;
}

.try-again-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #333;
    text-align: center;
}

.try-again-icon {
    font-size: 48px;
    margin-bottom: 10px;
}

.try-again-content p {
    margin: 0 0 5px 0;
    font-size: 18px;
    font-weight: bold;
}

.try-again-content small {
    font-size: 14px;
    opacity: 0.8;
}

/* Responsive design */
@media (max-width: 768px) {
    .match-teams {
        flex-direction: column;
        gap: 20px;
    }

    .team {
        max-width: 100%;
        flex-direction: column;
        align-items: center;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 12px;
        padding-top: 60px;
    }

    .team-logo {
        width: 80px;
        height: 80px;
        margin-bottom: 10px;
    }

    .team-logo img {
        width: 70px;
        height: 70px;
    }

    .team-score {
        font-size: 24px;
        padding: 8px 14px;
        top: 8px;
    }

    .match-center {
        margin: 15px 0;
        min-width: 120px;
    }

    .match-time-center {
        padding: 12px;
        min-height: 80px;
    }

    .center-time {
        font-size: 20px;
    }

    .match-info {
        flex-direction: column;
        gap: 10px;
        align-items: flex-start;
    }

    .broadcast-info {
        flex-direction: column;
        gap: 5px;
    }
}