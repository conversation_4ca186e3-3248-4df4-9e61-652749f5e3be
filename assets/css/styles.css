/* Base Styles */
body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #ffffff;
}

.container,
.mubashardev-container,
.halwasport-container {
    padding: 20px 0;
    max-width: 1200px;
    margin: 0 auto;
}

.data-container {
    margin-top: 20px;
}

.data-content {
    word-wrap: break-word;
    font-family: Arial, sans-serif;
    font-size: 14px;
    line-height: 1.4;
}

/* Tab Bar Styles */
.tab-bar {
    display: flex;
    background: #ffffff;
    border-radius: 14px;
    padding: 8px;
    margin-bottom: 28px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
    border: 1px solid #d1d5db;
}

.tab-item {
    flex: 1;
    text-align: center;
    padding: 12px 20px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    font-size: 16px;
    position: relative;
}

.tab-item:hover {
    background: rgba(255, 99, 68, 0.08);
    color: #FF6344;
}

.tab-item.active {
    background: #FF6344;
    color: white;
    box-shadow: 0 3px 10px rgba(255, 99, 68, 0.3);
}

.tab-item.active:hover {
    background: #e55039;
    color: white;
}

.tab-text {
    display: block;
    position: relative;
}

/* Loading Styles */
.loading-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;
}

.loader-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

.loading-info p {
    color: #666;
    font-size: 16px;
    margin: 0;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* Error and No Data Styles */
.error-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;
    color: #666;
}

.error-icon {
    font-size: 48px;
    margin-bottom: 20px;
    color: #dc3545;
}

.error-title {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
}

.error-description {
    font-size: 16px;
    color: #666;
    max-width: 400px;
    line-height: 1.5;
}

.no-matches {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;
    color: #666;
}

.no-matches-icon {
    font-size: 48px;
    margin-bottom: 20px;
    color: #6c757d;
}

.no-matches-title {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
}

.no-matches-description {
    font-size: 16px;
    color: #666;
}

/* Match Cards Styles */
.matches-container {
    display: flex;
    flex-direction: column;
    gap: 14px;
    padding: 0;
    margin: 0;
}

.match-card {
    background: #ffffff;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    padding: 16px;
    border: 1px solid #d1d5db;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.match-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #ddd, #ddd);
    transition: background 0.3s ease;
}

.match-card[style*="cursor: pointer"]:hover {
    transform: translateY(-6px);
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.18);
    border-color: #d1d5db;
}

.match-card[style*="cursor: pointer"]:hover::before {
    background: linear-gradient(90deg, #FF6344, #e55039);
}

.match-card[style*="cursor: pointer"]:hover .match-overlay {
    opacity: 1;
    visibility: visible;
}

/* Status-based card styling */
.match-card.status-live::before {
    background: linear-gradient(90deg, #ef4444, #dc2626) !important;
}

.match-card.status-finished::before {
    background: linear-gradient(90deg, #64748b, #475569) !important;
}

.match-card.status-coming-soon::before {
    background: linear-gradient(90deg, #f59e0b, #d97706) !important;
}

.match-card.status-not-started::before {
    background: linear-gradient(90deg, #10b981, #059669) !important;
}

/* Match Overlay */
.match-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 123, 255, 0.95);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    border-radius: 12px;
    z-index: 10;
}

.overlay-text {
    color: white;
    font-size: 18px;
    font-weight: bold;
    text-align: center;
    padding: 10px;
}

.status-live .match-overlay {
    background: rgba(220, 53, 69, 0.95);
}

.status-finished .match-overlay {
    background: rgba(108, 117, 125, 0.95);
}

.status-coming-soon .match-overlay {
    background: rgba(255, 193, 7, 0.95);
}

.status-not-started .match-overlay {
    background: rgba(40, 167, 69, 0.95);
}

/* Match Header */
.match-header {
    display: none;
}

/* Match Teams */
.match-teams {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    padding: 15px 0;
}

.team {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    max-width: 35%;
    position: relative;
}

.team-logo {
    width: 70px;
    height: 70px;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #ffffff;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid #d1d5db;
    transition: transform 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.match-card:hover .team-logo {
    transform: scale(1.05);
}

.team-logo img {
    width: 55px;
    height: 55px;
    object-fit: contain;
}

.team-name {
    font-weight: 600;
    font-size: 15px;
    color: #111827;
    text-align: center;
    margin-bottom: 8px;
    line-height: 1.3;
}

.team-score {
    display: none;
}

.home-team .team-score {
    right: -30px;
}

.away-team .team-score {
    left: -30px;
}

/* Match Center Status */
.match-center {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 0 0 auto;
    margin: 0 20px;
    min-width: 140px;
}

.match-time-center {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 6px;
    padding: 12px;
    border-radius: 12px;
    background: #ffffff;
    border: 2px solid #d1d5db;
    min-height: 70px;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.center-time {
    font-size: 20px;
    font-weight: bold;
    color: #111827;
    text-align: center;
    line-height: 1.2;
}

.center-status {
    font-size: 14px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    text-align: center;
    padding: 4px 12px;
    border-radius: 12px;
}

.center-status.live {
    background: #ef4444;
    color: white;
    animation: pulse 1.5s infinite;
    box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
}

.center-status.upcoming {
    background: #10b981;
    color: white;
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.center-status.finished {
    background: #64748b;
    color: white;
    box-shadow: 0 2px 8px rgba(100, 116, 139, 0.3);
}



/* Timer and Status Indicators */
.countdown-timer {
    font-family: 'Courier New', monospace;
    font-size: 16px;
    font-weight: bold;
    color: #333;
    background: #fff;
    padding: 6px 12px;
    border-radius: 8px;
    border: 1px solid #ddd;
    margin-top: 4px;
    min-width: 80px;
    text-align: center;
}

.live-indicator {
    font-family: Arial, sans-serif;
    font-size: 14px;
    font-weight: bold;
    color: #fff;
    background: #dc3545;
    padding: 6px 12px;
    border-radius: 8px;
    margin-top: 4px;
    text-align: center;
    animation: pulse 1.5s infinite;
    text-transform: uppercase;
}

.finished-indicator {
    font-family: Arial, sans-serif;
    font-size: 14px;
    font-weight: bold;
    color: #fff;
    background: #6c757d;
    padding: 6px 12px;
    border-radius: 8px;
    margin-top: 4px;
    text-align: center;
    text-transform: uppercase;
}

@keyframes pulse {
    0% {
        opacity: 1;
    }

    50% {
        opacity: 0.5;
    }

    100% {
        opacity: 1;
    }
}

/* Match Info */
.match-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 8px;
    border-top: 1px solid #eee;
    margin-bottom: 8px;
}

.competition {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: #374151;
    font-size: 14px;
}

.broadcast-info {
    display: flex;
    gap: 20px;
}

.commentary,
.tv {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    color: #6b7280;
}

/* Simple icons using CSS */
.icon-trophy::before {
    content: "🏆";
    margin-right: 4px;
}

.icon-mic::before {
    content: "🎤";
    margin-right: 4px;
}

.icon-tv::before {
    content: "📺";
    margin-right: 4px;
}

/* Card Loader Overlay */
.card-loader-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 123, 255, 0.95);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 20;
    border-radius: 12px;
}

.card-loader-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: white;
    text-align: center;
}

.card-loader-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

.card-loader-content p {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

/* Try Again Overlay */
.try-again-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 193, 7, 0.95);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 20;
    border-radius: 12px;
}

.try-again-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #333;
    text-align: center;
}

.try-again-icon {
    font-size: 48px;
    margin-bottom: 10px;
}

.try-again-content p {
    margin: 0 0 5px 0;
    font-size: 18px;
    font-weight: bold;
}

.try-again-content small {
    font-size: 14px;
    opacity: 0.8;
}

/* Responsive Design - Maintaining Same Layout */
.container {
    padding: 20px 0;
    max-width: 1200px;
    margin: 0 auto;
}

/* Large screens (1200px+) */
@media (min-width: 1200px) {
    .match-card {
        padding: 20px;
    }

    .team-logo {
        width: 80px;
        height: 80px;
    }

    .team-logo img {
        width: 65px;
        height: 65px;
    }

    .center-time {
        font-size: 22px;
    }
}

/* Medium screens (768px - 1199px) */
@media (min-width: 768px) and (max-width: 1199px) {

    .container,
    .halwasport-container {
        padding: 16px 0;
    }

    .match-card {
        padding: 14px;
    }

    .team-logo {
        width: 65px;
        height: 65px;
    }

    .team-logo img {
        width: 50px;
        height: 50px;
    }

    .match-center {
        min-width: 90px;
        margin: 0 10px;
    }

    .match-time-center {
        padding: 10px;
        min-height: 65px;
    }

    .center-time {
        font-size: 18px;
    }

    .team-name {
        font-size: 14px;
    }

    .competition {
        font-size: 13px;
    }

    .commentary,
    .tv {
        font-size: 11px;
    }
}

/* Small screens (480px - 767px) */
@media (min-width: 480px) and (max-width: 767px) {

    .container,
    .halwasport-container {
        padding: 12px 0;
    }

    .match-card {
        padding: 14px;
        margin-bottom: 16px;
    }

    .match-teams {
        gap: 8px;
    }

    .team {
        max-width: 30%;
    }

    .team-logo {
        width: 65px;
        height: 65px;
        margin-bottom: 8px;
    }

    .team-logo img {
        width: 50px;
        height: 50px;
    }

    .team-name {
        font-size: 12px;
        line-height: 1.2;
    }

    .match-center {
        min-width: 90px;
        margin: 0 8px;
    }

    .match-time-center {
        padding: 10px;
        min-height: 70px;
    }

    .center-time {
        font-size: 16px;
    }

    .center-status {
        font-size: 11px;
        padding: 2px 8px;
    }

    .tab-item {
        padding: 8px 12px;
        font-size: 13px;
    }

    .match-info {
        padding-top: 8px;
        margin-bottom: 8px;
    }

    .competition {
        font-size: 12px;
    }

    .commentary,
    .tv {
        font-size: 10px;
    }

    .broadcast-info {
        gap: 12px;
    }
}

/* Extra small screens (320px - 479px) */
@media (max-width: 479px) {

    .container,
    .halwasport-container {
        padding: 8px 0;
    }

    .match-card {
        padding: 10px;
        margin-bottom: 12px;
    }

    .match-teams {
        gap: 6px;
    }

    .team {
        max-width: 28%;
    }

    .team-logo {
        width: 55px;
        height: 55px;
        margin-bottom: 6px;
    }

    .team-logo img {
        width: 42px;
        height: 42px;
    }

    .team-name {
        font-size: 11px;
        line-height: 1.1;
    }

    .match-center {
        min-width: 80px;
        margin: 0 6px;
    }

    .match-time-center {
        padding: 8px;
        min-height: 60px;
    }

    .center-time {
        font-size: 14px;
    }

    .center-status {
        font-size: 10px;
        padding: 2px 6px;
    }

    .tab-item {
        padding: 6px 10px;
        font-size: 12px;
    }

    .tab-bar {
        padding: 4px;
        margin-bottom: 16px;
    }

    .match-info {
        padding-top: 6px;
        margin-bottom: 6px;
    }

    .competition {
        font-size: 11px;
    }

    .commentary,
    .tv {
        font-size: 9px;
    }

    .broadcast-info {
        gap: 15px;
    }
}

/* Additional mobile improvements */
@media (max-width: 767px) {
    .match-card {
        min-height: 120px;
        touch-action: manipulation;
    }

    .match-card:active {
        transform: scale(0.98);
    }

    .team-name {
        word-break: break-word;
        hyphens: auto;
    }

    .match-overlay {
        border-radius: 16px;
    }

    .overlay-text {
        font-size: 14px;
        padding: 8px 12px;
    }
}