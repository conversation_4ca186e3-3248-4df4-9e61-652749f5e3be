// DOM Elements
const loadingInfo = document.getElementById('loadingInfo');
const statusDiv = document.getElementById('status');
const dataContainer = document.getElementById('dataContainer');
const dataContent = document.getElementById('dataContent');

// Configuration
const TARGET_URL = 'https://fawanews.co.uk/';
const TARGET_SELECTOR = '#content > div > div > div > div.nv-content-wrap.entry-content > div.AlbaSportFixture.asp-font';

// Hover text mapping for clickability
const HOVER_TEXT_MAPPING = {
    "Soon": "Comming soon",
    "Live": "live",
    "Show": "Watch The Match",
    "NotStarted": "Not started",
    "FinMatch": "Match has finished",
    "Finished": "Finished",
    "NoMatches": "There are no matches Today"
};

// Clickable hover texts (Live and Show)
const CLICKABLE_TEXTS = [
    HOVER_TEXT_MAPPING.Live,      // "live"
    HOVER_TEXT_MAPPING.Show,      // "Watch The Match"
    "live",                       // direct match
    "Watch The Match"             // direct match
];

// CORS proxy options
const CORS_PROXIES = [
    'https://api.allorigins.win/get?url=',
    'https://cors-anywhere.herokuapp.com/',
    'https://api.codetabs.com/v1/proxy?quest='
];

// Utility Functions
function showStatus(message, type) {
    // Only show error messages, hide success/loading messages
    if (type === 'error') {
        statusDiv.innerHTML = `
        <div class="error-message">
            <div class="error-icon">⚠️</div>
            <div class="error-title">Unable to Load Matches</div>
            <div class="error-description">${message}</div>
        </div>`;
        statusDiv.style.display = 'block';
    } else {
        statusDiv.style.display = 'none';
    }
}

function hideStatus() {
    statusDiv.style.display = 'none';
}

function showData(data) {
    dataContent.innerHTML = data;
    dataContainer.style.display = 'block';
    // Start countdown timers after data is displayed
    setTimeout(startCountdownTimers, 100);
}

function hideData() {
    dataContainer.style.display = 'none';
}

function hideLoading() {
    loadingInfo.style.display = 'none';
}

// Data Parsing Functions
function parseHtmlContent(html) {
    // Create a temporary DOM parser
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, 'text/html');

    // Try to find the target element
    const targetElement = doc.querySelector(TARGET_SELECTOR);

    if (targetElement) {
        return parseMatchesData(targetElement);
    } else {
        // Try alternative selectors
        const alternatives = [
            'div.AlbaSportFixture',
            '.AlbaSportFixture',
            '[class*="AlbaSportFixture"]'
        ];

        for (const selector of alternatives) {
            const element = doc.querySelector(selector);
            if (element) {
                return parseMatchesData(element);
            }
        }

        return 'Target element not found with any selector.';
    }
}

function parseMatchesData(fixtureElement) {
    const matches = [];

    // Get all match elements
    const matchElements = fixtureElement.querySelectorAll('.AF_Match');

    matchElements.forEach((matchEl, index) => {
        const match = {
            id: index + 1,
            status: getMatchStatus(matchEl),
            startTime: matchEl.getAttribute('data-start') || '',
            homeTeam: extractTeamData(matchEl, '.AF_FTeam'),
            awayTeam: extractTeamData(matchEl, '.AF_STeam'),
            score: extractScore(matchEl),
            time: extractMatchTime(matchEl),
            timezone: extractTimezone(matchEl),
            competition: extractCompetition(matchEl),
            broadcastInfo: extractBroadcastInfo(matchEl),
            matchUrl: extractMatchUrl(matchEl),
            hoverText: extractHoverText(matchEl)
        };

        matches.push(match);
    });

    return createMatchCards(matches);
}

function getMatchStatus(matchEl) {
    if (matchEl.classList.contains('finished')) return 'finished';
    if (matchEl.classList.contains('live')) return 'live';
    if (matchEl.classList.contains('comming-soon')) return 'coming-soon';
    if (matchEl.classList.contains('not-started')) return 'not-started';
    return 'unknown';
}

function extractTeamData(matchEl, selector) {
    const teamEl = matchEl.querySelector(selector);
    if (!teamEl) return { name: 'Unknown', logo: '' };

    const nameEl = teamEl.querySelector('.AF_TeamName');
    const logoEl = teamEl.querySelector('.AF_TeamLogo img');

    return {
        name: nameEl ? nameEl.textContent.trim() : 'Unknown',
        logo: logoEl ? logoEl.src : ''
    };
}

function extractScore(matchEl) {
    const resultElements = matchEl.querySelectorAll('.result');
    if (resultElements.length >= 2) {
        return {
            home: resultElements[0].textContent.trim(),
            away: resultElements[1].textContent.trim()
        };
    }
    return { home: '0', away: '0' };
}

function extractMatchTime(matchEl) {
    const timeEl = matchEl.querySelector('.AF_EvTime');
    const statusEl = matchEl.querySelector('.AF_StaText');

    return {
        scheduled: timeEl ? timeEl.textContent.trim() : '',
        status: statusEl ? statusEl.textContent.trim() : ''
    };
}

function extractTimezone(matchEl) {
    const timezoneEl = matchEl.querySelector('.asp-city');
    return timezoneEl ? timezoneEl.textContent.trim() : 'Unknown';
}

function extractCompetition(matchEl) {
    const competitionEl = matchEl.querySelector('.cup');
    return competitionEl ? competitionEl.textContent.trim() : 'Unknown';
}

function extractBroadcastInfo(matchEl) {
    const micEl = matchEl.querySelector('.mic');
    const tvEl = matchEl.querySelector('.tv');

    return {
        commentary: micEl ? micEl.textContent.trim() : 'Unknown',
        tv: tvEl ? tvEl.textContent.trim() : 'Unknown'
    };
}

function extractMatchUrl(matchEl) {
    const linkEl = matchEl.querySelector('.AF_EventMask');
    return linkEl ? linkEl.href : '';
}

function extractHoverText(matchEl) {
    const hoverTextEl = matchEl.querySelector('.AF_MaskText');
    return hoverTextEl ? hoverTextEl.textContent.trim() : '';
}

// Status and Display Functions
function getStatusDisplayText(status, statusText) {
    switch (status) {
        case 'live': return 'LIVE';
        case 'finished': return 'FINISHED';
        case 'coming-soon': return 'COMING SOON';
        case 'not-started': return statusText || 'NOT STARTED';
        default: return statusText || 'UNKNOWN';
    }
}

function isMatchClickable(hoverText) {
    if (!hoverText) return false;

    // Check if hover text matches any clickable text (case insensitive)
    return CLICKABLE_TEXTS.some(clickableText =>
        hoverText.toLowerCase() === clickableText.toLowerCase()
    );
}

function getDisplayHoverText(hoverText) {
    if (!hoverText) return 'View Match';

    // Return the actual hover text from the source
    return hoverText;
}

function getMatchPriority(hoverText) {
    if (!hoverText) return 4;

    const text = hoverText.toLowerCase();

    // Priority order: Live (1) > Coming Soon (2) > Not Started (3) > Finished (4)
    if (text.includes('live') || text === 'watch the match') {
        return 1; // Highest priority - Live matches
    } else if (text.includes('comming soon')) {
        return 2; // Coming soon matches
    } else if (text.includes('not started')) {
        return 3; // Not started matches
    } else if (text.includes('finished') || text === 'match has finished') {
        return 4; // Lowest priority - Finished matches
    }

    return 4; // Default to lowest priority
}

function sortMatchesByPriority(matches) {
    return matches.sort((a, b) => {
        const priorityA = getMatchPriority(a.hoverText);
        const priorityB = getMatchPriority(b.hoverText);

        // Sort by priority (lower number = higher priority)
        if (priorityA !== priorityB) {
            return priorityA - priorityB;
        }

        // If same priority, sort by scheduled time
        if (a.time.scheduled && b.time.scheduled) {
            return a.time.scheduled.localeCompare(b.time.scheduled);
        }

        return 0;
    });
}

function getCountdownHtml(match) {
    // Use the hover text to determine what to show in center
    const hoverText = match.hoverText.toLowerCase();

    if (hoverText.includes('live') || hoverText === 'watch the match') {
        return `<div class="live-indicator">LIVE NOW</div>`;
    } else if (hoverText.includes('finished') || hoverText === 'match has finished') {
        return `<div class="finished-indicator">ENDED</div>`;
    } else if (hoverText.includes('not started')) {
        // Show countdown timer for not started matches
        return `<div class="countdown-timer" id="timer-${match.id}">Starting Soon</div>`;
    } else if (hoverText.includes('comming soon')) {
        return `<div class="countdown-timer" id="timer-${match.id}">Coming Soon</div>`;
    }

    return `<div class="countdown-timer">${match.hoverText}</div>`;
}

async function handleMatchClick(url, matchId) {
    const match = window.matchesData?.find(m => m.id == matchId);
    if (!match) return;

    // Only allow clicking if hover text indicates it's clickable
    if (!isMatchClickable(match.hoverText)) {
        return;
    }

    if (!url) return;

    // Show loader overlay on the clicked card
    const card = document.querySelector(`[data-match-id="${matchId}"]`);
    if (card) {
        showCardLoader(card);
    }

    try {
        // Fetch the match page and check for iframe
        const iframeUrl = await fetchAndCheckIframe(url);

        if (iframeUrl) {
            // Open iframe URL in new tab
            window.open(iframeUrl, '_blank');
        } else {
            // Show try again later message
            showTryAgainMessage(card);
        }
    } catch (error) {
        console.error('Error fetching match page:', error);
        showTryAgainMessage(card);
    } finally {
        // Hide loader after 2 seconds
        setTimeout(() => {
            if (card) {
                hideCardLoader(card);
            }
        }, 2000);
    }
}

function startCountdownTimers() {
    // Add visual effects to timer elements
    const timers = document.querySelectorAll('.countdown-timer');
    timers.forEach(timer => {
        timer.style.animation = 'pulse 2s infinite';
    });

    const liveIndicators = document.querySelectorAll('.live-indicator');
    liveIndicators.forEach(indicator => {
        indicator.style.animation = 'pulse 1.5s infinite';
    });
}

// Card Loader Functions
function showCardLoader(card) {
    // Create loader overlay
    const loaderOverlay = document.createElement('div');
    loaderOverlay.className = 'card-loader-overlay';
    loaderOverlay.innerHTML = `
        <div class="card-loader-content">
            <div class="card-loader-spinner"></div>
            <p>Checking stream...</p>
        </div>
    `;

    card.appendChild(loaderOverlay);

    // Trigger animation
    setTimeout(() => {
        loaderOverlay.style.opacity = '1';
    }, 10);
}

function hideCardLoader(card) {
    const loaderOverlay = card.querySelector('.card-loader-overlay');
    if (loaderOverlay) {
        loaderOverlay.style.opacity = '0';
        setTimeout(() => {
            if (loaderOverlay.parentNode) {
                loaderOverlay.parentNode.removeChild(loaderOverlay);
            }
        }, 300);
    }
}

function showTryAgainMessage(card) {
    // Remove existing loader
    hideCardLoader(card);

    // Create try again overlay
    const tryAgainOverlay = document.createElement('div');
    tryAgainOverlay.className = 'try-again-overlay';
    tryAgainOverlay.innerHTML = `
        <div class="try-again-content">
            <div class="try-again-icon">⏰</div>
            <p>Try Again Later</p>
            <small>Stream not available right now</small>
        </div>
    `;

    card.appendChild(tryAgainOverlay);

    // Show animation
    setTimeout(() => {
        tryAgainOverlay.style.opacity = '1';
    }, 10);

    // Auto-hide after 3 seconds
    setTimeout(() => {
        if (tryAgainOverlay.parentNode) {
            tryAgainOverlay.style.opacity = '0';
            setTimeout(() => {
                if (tryAgainOverlay.parentNode) {
                    tryAgainOverlay.parentNode.removeChild(tryAgainOverlay);
                }
            }, 300);
        }
    }, 3000);
}

// Iframe Detection Function
async function fetchAndCheckIframe(url) {
    try {
        // Try direct request first
        let html;
        try {
            const response = await fetch(url, {
                method: 'GET',
                mode: 'cors',
                headers: {
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                    'User-Agent': 'Mozilla/5.0 (compatible; DataFetcher/1.0)'
                }
            });

            if (response.ok) {
                html = await response.text();
            } else {
                throw new Error('Direct request failed');
            }
        } catch (directError) {
            // Try with proxy if direct request fails
            for (const proxy of CORS_PROXIES) {
                try {
                    let requestUrl;

                    if (proxy.includes('allorigins')) {
                        requestUrl = `${proxy}${encodeURIComponent(url)}`;
                    } else {
                        requestUrl = `${proxy}${url}`;
                    }

                    const response = await fetch(requestUrl);

                    if (response.ok) {
                        if (proxy.includes('allorigins')) {
                            const data = await response.json();
                            html = data.contents;
                        } else {
                            html = await response.text();
                        }
                        break;
                    }
                } catch (proxyError) {
                    console.log(`Proxy ${proxy} failed for iframe check:`, proxyError);
                    continue;
                }
            }
        }

        if (!html) {
            throw new Error('Could not fetch page content');
        }

        // Parse HTML and look for iframe
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');

        // Look for iframe elements
        const iframes = doc.querySelectorAll('iframe');

        if (iframes.length > 0) {
            // Return the first iframe src
            const iframeSrc = iframes[0].src;

            // If iframe src is relative, make it absolute
            if (iframeSrc.startsWith('/')) {
                const urlObj = new URL(url);
                return `${urlObj.protocol}//${urlObj.host}${iframeSrc}`;
            } else if (iframeSrc.startsWith('http')) {
                return iframeSrc;
            } else if (iframeSrc) {
                // Handle other relative URLs
                const urlObj = new URL(url);
                return `${urlObj.protocol}//${urlObj.host}/${iframeSrc}`;
            }
        }

        return null; // No iframe found

    } catch (error) {
        console.error('Error checking for iframe:', error);
        return null;
    }
}

// Card Creation Function
function createMatchCards(matches) {
    if (matches.length === 0) {
        return `
        <div class="no-matches">
            <div class="no-matches-icon">⚽</div>
            <div class="no-matches-title">No Matches Available</div>
            <div class="no-matches-description">There are no matches scheduled at this time.</div>
        </div>`;
    }

    // Sort matches to put live ones first
    const sortedMatches = sortMatchesByPriority(matches);

    // Store matches data globally for timer updates
    window.matchesData = sortedMatches;

    let html = `<div class="matches-container">`;

    sortedMatches.forEach(match => {
        const statusClass = `status-${match.status}`;
        const statusText = getStatusDisplayText(match.status, match.time.status);
        const overlayText = getDisplayHoverText(match.hoverText);
        const countdownHtml = getCountdownHtml(match);
        const isClickable = isMatchClickable(match.hoverText);
        const cursorStyle = isClickable ? 'cursor: pointer;' : 'cursor: default; opacity: 0.7;';

        html += `
        <div class="match-card ${statusClass}" data-match-id="${match.id}"
             onclick="${isClickable ? `handleMatchClick('${match.matchUrl}', ${match.id})` : ''}"
             style="${cursorStyle}">
            ${isClickable ? `
            <div class="match-overlay">
                <div class="overlay-text">${overlayText}</div>
            </div>
            ` : ''}

            <div class="match-header">
                <div class="match-time">
                    <div class="scheduled-time">${match.time.scheduled}</div>
                    <div class="timezone">${match.timezone}</div>
                </div>
            </div>

            <div class="match-teams">
                <div class="team home-team">
                    <div class="team-logo">
                        <img src="${match.homeTeam.logo}" alt="${match.homeTeam.name}" onerror="this.style.display='none'">
                    </div>
                    <div class="team-name">${match.homeTeam.name}</div>
                    <div class="team-score">${match.score.home}</div>
                </div>

                <div class="match-center">
                    <div class="match-status-center">
                        <div class="status-badge">
                            <span class="status-indicator"></span>
                            ${statusText}
                        </div>
                        ${countdownHtml}
                    </div>
                </div>

                <div class="team away-team">
                    <div class="team-score">${match.score.away}</div>
                    <div class="team-name">${match.awayTeam.name}</div>
                    <div class="team-logo">
                        <img src="${match.awayTeam.logo}" alt="${match.awayTeam.name}" onerror="this.style.display='none'">
                    </div>
                </div>
            </div>

            <div class="match-info">
                <div class="competition">
                    <i class="icon-trophy"></i>
                    ${match.competition}
                </div>
                <div class="broadcast-info">
                    <div class="commentary">
                        <i class="icon-mic"></i>
                        ${match.broadcastInfo.commentary}
                    </div>
                    <div class="tv">
                        <i class="icon-tv"></i>
                        ${match.broadcastInfo.tv}
                    </div>
                </div>
            </div>
        </div>`;
    });

    html += `</div>`;
    return html;
}

// Fetch Functions
async function fetchWithDirectRequest() {
    try {
        const response = await fetch(TARGET_URL, {
            method: 'GET',
            mode: 'cors',
            headers: {
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'User-Agent': 'Mozilla/5.0 (compatible; DataFetcher/1.0)'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const html = await response.text();
        return parseHtmlContent(html);

    } catch (error) {
        console.error('Direct request failed:', error);
        throw new Error(`Direct request failed: ${error.message}`);
    }
}

async function fetchWithProxy(proxyUrl) {
    try {
        let requestUrl;

        if (proxyUrl.includes('allorigins')) {
            requestUrl = `${proxyUrl}${encodeURIComponent(TARGET_URL)}`;
        } else {
            requestUrl = `${proxyUrl}${TARGET_URL}`;
        }

        const response = await fetch(requestUrl);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        let html;
        if (proxyUrl.includes('allorigins')) {
            const data = await response.json();
            html = data.contents;
        } else {
            html = await response.text();
        }

        return parseHtmlContent(html);

    } catch (error) {
        console.error(`Proxy request failed (${proxyUrl}):`, error);
        throw new Error(`Proxy request failed: ${error.message}`);
    }
}

// Main Fetch Function
async function fetchData() {
    hideData();

    try {
        // Try direct request first
        try {
            const data = await fetchWithDirectRequest();
            hideLoading();
            showData(data);
            return;
        } catch (directError) {
            console.log('Direct request failed, trying proxies...');
        }

        // Try each proxy
        for (const proxy of CORS_PROXIES) {
            try {
                const data = await fetchWithProxy(proxy);
                hideLoading();
                showData(data);
                return;
            } catch (proxyError) {
                console.log(`Proxy ${proxy} failed:`, proxyError);
                continue;
            }
        }

        // If all methods fail
        throw new Error('Please check your internet connection and try refreshing the page.');

    } catch (error) {
        hideLoading();
        showStatus(error.message, 'error');
        console.error('Fetch error:', error);
    }
}

// Initialize
window.addEventListener('load', fetchData);

console.log('Football Matches script loaded');
console.log('Target URL:', TARGET_URL);
console.log('Target Selector:', TARGET_SELECTOR);
