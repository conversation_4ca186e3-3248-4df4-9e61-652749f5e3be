// DOM Elements
const loadingInfo = document.getElementById('loadingInfo');
const statusDiv = document.getElementById('status');
const dataContainer = document.getElementById('dataContainer');
const dataContent = document.getElementById('dataContent');
const tabBar = document.getElementById('tabBar');

// Backend endpoint
const BACKEND_URL = 'backend.php';

// Current filter state
let currentFilter = 'today';

// Utility Functions
function showLoading() {
    loadingInfo.style.display = 'flex';
    hideData();
    hideTabBar();
}

function hideLoading() {
    loadingInfo.style.display = 'none';
}

function showData(data) {
    dataContent.innerHTML = data;
    dataContainer.style.display = 'block';
    showTabBar();
    setTimeout(startCountdownTimers, 100);
}

function hideData() {
    dataContainer.style.display = 'none';
}

function showTabBar() {
    tabBar.style.display = 'flex';
}

function hideTabBar() {
    tabBar.style.display = 'none';
}

// Data Fetching Functions
async function fetchData(filter = 'today') {
    try {
        showLoading();
        currentFilter = filter;

        const response = await fetch(`${BACKEND_URL}?filter=${encodeURIComponent(filter)}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error('Network response was not ok');
        }

        const result = await response.json();

        if (result.success) {
            showData(result.data);
        } else {
            showData(result.data); // Error message from backend
        }

        hideLoading();

    } catch (error) {
        hideLoading();
        showData(createErrorMessage());
        console.error('Fetch error:', error);
    }
}

function createErrorMessage() {
    return `
    <div class="no-matches">
        <div class="no-matches-icon">📋</div>
        <div class="no-matches-title">No Events Available</div>
        <div class="no-matches-description">There are no events scheduled at the moment. Please check back later.</div>
    </div>`;
}

// Tab switching functionality
function switchTab(filter) {
    // Update active tab
    document.querySelectorAll('.tab-item').forEach(tab => {
        tab.classList.remove('active');
    });
    document.querySelector(`[data-filter="${filter}"]`).classList.add('active');

    // Fetch new data for the selected filter
    fetchData(filter);
}

// Match click handler for iframe extraction
async function handleMatchClick(matchUrl, matchId) {
    if (!matchUrl) return;

    const card = document.querySelector(`[data-match-id="${matchId}"]`);
    if (!card) return;

    // Show loading overlay
    showMatchLoading(card);

    try {
        // Try to extract iframe URL from the match page
        const iframeUrl = await extractIframeUrl(matchUrl);

        if (iframeUrl) {
            // Open the iframe URL in a new tab
            window.open(iframeUrl, '_blank');
            hideMatchLoading(card);
        } else {
            // Show try again later message
            showTryAgainMessage(card);
        }
    } catch (error) {
        console.error('Error extracting iframe:', error);
        showTryAgainMessage(card);
    }
}

// Helper functions for match loading states
function showMatchLoading(card) {
    const overlay = document.createElement('div');
    overlay.className = 'click-loading-overlay';
    overlay.innerHTML = `
        <div class="click-loading-content">
            <div class="click-loader"></div>
            <div class="click-loading-text">Checking stream...</div>
        </div>
    `;
    card.appendChild(overlay);
}

function hideMatchLoading(card) {
    const overlay = card.querySelector('.click-loading-overlay');
    if (overlay) {
        setTimeout(() => overlay.remove(), 2000);
    }
}

function showTryAgainMessage(card) {
    const overlay = card.querySelector('.click-loading-overlay');
    if (overlay) {
        overlay.innerHTML = `
            <div class="click-loading-content try-again">
                <div class="try-again-icon">⏰</div>
                <div class="try-again-title">Try Again Later</div>
                <div class="try-again-text">Stream not available right now</div>
            </div>
        `;
        setTimeout(() => overlay.remove(), 3000);
    }
}

// Iframe extraction for clickable matches
async function extractIframeUrl(matchUrl) {
    try {
        // Make request to backend to extract iframe URL
        const response = await fetch(`iframe-extractor.php?url=${encodeURIComponent(matchUrl)}`);

        if (response.ok) {
            const result = await response.json();
            return result.iframeUrl || null;
        }
    } catch (error) {
        console.error('Error extracting iframe:', error);
    }

    return null;
}

// Simplified countdown timer (if needed)
function startCountdownTimers() {
    // Add visual effects to live indicators
    const liveIndicators = document.querySelectorAll('.center-status.live');
    liveIndicators.forEach(indicator => {
        indicator.style.animation = 'pulse 1.5s infinite';
    });
}

// Tab switching functionality
function switchTab(filter) {
    // Update active tab
    document.querySelectorAll('.tab-item').forEach(tab => {
        tab.classList.remove('active');
    });
    document.querySelector(`[data-filter="${filter}"]`).classList.add('active');

    // Fetch new data for the selected filter
    fetchData(filter);
}

// Tab click event listeners
function initializeTabs() {
    document.querySelectorAll('.tab-item').forEach(tab => {
        tab.addEventListener('click', () => {
            const filter = tab.getAttribute('data-filter');
            switchTab(filter);
        });
    });
}

// Initialize
window.addEventListener('load', () => {
    fetchData();
    initializeTabs();
});

console.log('Football Matches script loaded - Server-side version');






