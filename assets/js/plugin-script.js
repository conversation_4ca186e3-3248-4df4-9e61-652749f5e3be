/**
 * HalwaSport Plugin JavaScript
 *
 * @package HalwaSport
 * @since 1.0.0
 */

jQuery(document).ready(function ($) {
    // DOM Elements with plugin-specific IDs
    const loadingInfo = document.getElementById('halwasportLoadingInfo');
    const statusDiv = document.getElementById('halwasportStatus');
    const dataContainer = document.getElementById('halwasportDataContainer');
    const dataContent = document.getElementById('halwasportDataContent');
    const tabBar = document.getElementById('halwasportTabBar');

    // Current filter state and data storage
    let currentFilter = 'today';
    let allMatchesData = null;

    // Utility Functions
    function showLoading() {
        if (loadingInfo) loadingInfo.style.display = 'flex';
        hideData();
        hideTabBar();
    }

    function hideLoading() {
        if (loadingInfo) loadingInfo.style.display = 'none';
    }

    function showData(data) {
        if (dataContent) dataContent.innerHTML = data;
        if (dataContainer) dataContainer.style.display = 'block';
        showTabBar();
        setTimeout(startCountdownTimers, 100);
    }

    function hideData() {
        if (dataContainer) dataContainer.style.display = 'none';
    }

    function showTabBar() {
        if (tabBar) tabBar.style.display = 'flex';
    }

    function hideTabBar() {
        if (tabBar) tabBar.style.display = 'none';
    }

    // Data Fetching Functions
    async function fetchData(filter = 'today') {
        try {
            showLoading();
            currentFilter = filter;

            // Only fetch from server if we don't have data yet
            if (!allMatchesData) {
                const formData = new FormData();
                formData.append('action', 'halwasport_get_matches');
                formData.append('filter', filter);
                formData.append('nonce', halwasport_ajax.nonce);

                const response = await fetch(halwasport_ajax.ajax_url, {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }

                const result = await response.json();

                if (result.success) {
                    // Store all matches data for client-side filtering
                    allMatchesData = result.allMatches;
                    showData(result.data);
                } else {
                    showData(result.data); // Error message from backend
                }
            } else {
                // Use cached data and filter client-side
                const filteredData = filterAndGenerateCards(filter);
                showData(filteredData);
            }

            hideLoading();

        } catch (error) {
            hideLoading();
            showData(createErrorMessage());
            console.error('Fetch error:', error);
        }
    }

    function createErrorMessage() {
        return `
        <div class="no-matches">
            <div class="no-matches-icon">📋</div>
            <div class="no-matches-title">No Events Available</div>
            <div class="no-matches-description">There are no events scheduled at the moment. Please check back later.</div>
        </div>`;
    }

    // Client-side filtering function
    function filterAndGenerateCards(filter) {
        if (!allMatchesData) {
            return createErrorMessage();
        }

        // Get matches for the specific filter
        let filteredMatches = [];
        switch (filter) {
            case 'yesterday':
                filteredMatches = allMatchesData.yesterday || [];
                break;
            case 'today':
                filteredMatches = allMatchesData.today || [];
                break;
            case 'tomorrow':
                filteredMatches = allMatchesData.tomorrow || [];
                break;
            default:
                filteredMatches = allMatchesData.today || [];
        }

        if (filteredMatches.length === 0) {
            return createEmptyStateMessage(filter);
        }

        // Sort matches by priority (live first)
        const sortedMatches = sortMatchesByPriority(filteredMatches);

        // Generate HTML
        let html = '<div class="matches-container">';

        sortedMatches.forEach(match => {
            html += createMatchCard(match);
        });

        html += '</div>';
        return html;
    }

    function createEmptyStateMessage(filter) {
        const messages = {
            today: {
                icon: '📅',
                title: 'No Events Today',
                description: 'There are no events scheduled for today. Check other days for upcoming events!'
            },
            yesterday: {
                icon: '⏮️',
                title: 'No Events Yesterday',
                description: 'There were no events yesterday. Check today\'s or tomorrow\'s schedule!'
            },
            tomorrow: {
                icon: '⏭️',
                title: 'No Events Tomorrow',
                description: 'There are no events scheduled for tomorrow yet. Check back later for updates!'
            }
        };

        const message = messages[filter] || messages.today;

        return `
        <div class="no-matches">
            <div class="no-matches-icon">${message.icon}</div>
            <div class="no-matches-title">${message.title}</div>
            <div class="no-matches-description">${message.description}</div>
        </div>`;
    }

    // Helper functions for client-side processing
    function sortMatchesByPriority(matches) {
        return matches.sort((a, b) => {
            const priorityA = getMatchPriority(a.hoverText);
            const priorityB = getMatchPriority(b.hoverText);

            if (priorityA !== priorityB) {
                return priorityA - priorityB;
            }

            if (a.time.scheduled && b.time.scheduled) {
                return a.time.scheduled.localeCompare(b.time.scheduled);
            }

            return 0;
        });
    }

    function getMatchPriority(hoverText) {
        if (!hoverText) return 4;

        const text = hoverText.toLowerCase();

        if (text.includes('live') || text === 'watch the match') {
            return 1; // Highest priority - Live matches
        } else if (text.includes('comming soon')) {
            return 2; // Coming soon matches
        } else if (text.includes('not started')) {
            return 3; // Not started matches
        } else if (text.includes('finished') || text === 'match has finished') {
            return 4; // Lowest priority - Finished matches
        }

        return 4; // Default to lowest priority
    }

    function createMatchCard(match) {
        const statusClass = `status-${match.status}`;
        const overlayText = match.hoverText || 'View Match';
        const centerContent = getCenterContent(match);
        const isClickable = isMatchClickable(match.hoverText);
        const cursorStyle = isClickable ? 'cursor: pointer;' : 'cursor: default; opacity: 0.7;';

        const overlayHtml = isClickable ? `
            <div class="match-overlay">
                <div class="overlay-text">${overlayText}</div>
            </div>` : '';

        const clickHandler = isClickable ? `onclick="halwasportHandleMatchClick('${match.matchUrl}', ${match.id})"` : '';

        return `
        <div class="match-card ${statusClass}" data-match-id="${match.id}" 
             ${clickHandler} style="${cursorStyle}">
            ${overlayHtml}
            
            <div class="match-teams">
                <div class="team home-team">
                    <div class="team-logo">
                        <img src="${match.homeTeam.logo}" alt="${match.homeTeam.name}" onerror="this.style.display='none'">
                    </div>
                    <div class="team-name">${match.homeTeam.name}</div>
                </div>
                
                <div class="match-center">
                    <div class="match-time-center">
                        <div class="center-time">${centerContent.time}</div>
                        <div class="center-status ${centerContent.statusClass}">${centerContent.statusText}</div>
                    </div>
                </div>
                
                <div class="team away-team">
                    <div class="team-logo">
                        <img src="${match.awayTeam.logo}" alt="${match.awayTeam.name}" onerror="this.style.display='none'">
                    </div>
                    <div class="team-name">${match.awayTeam.name}</div>
                </div>
            </div>
            
            <div class="match-info">
                <div class="competition">
                    <i class="icon-trophy"></i>
                    ${match.competition} • ${match.timezone}
                </div>
                <div class="broadcast-info">
                    <div class="commentary">
                        <i class="icon-mic"></i>
                        ${match.broadcastInfo.commentary}
                    </div>
                    <div class="tv">
                        <i class="icon-tv"></i>
                        ${match.broadcastInfo.tv}
                    </div>
                </div>
            </div>
        </div>`;
    }

    function getCenterContent(match) {
        const hoverText = match.hoverText.toLowerCase();
        let statusClass = '';
        let statusText = '';

        if (hoverText.includes('live') || hoverText === 'watch the match') {
            statusClass = 'live';
            statusText = 'Live';
        } else if (hoverText.includes('finished') || hoverText === 'match has finished') {
            statusClass = 'finished';
            statusText = 'Match Finished';
        } else if (hoverText.includes('not started') || hoverText.includes('comming soon')) {
            statusClass = 'upcoming';
            statusText = 'Upcoming';
        } else {
            statusClass = 'upcoming';
            statusText = 'Upcoming';
        }

        return {
            time: match.time.scheduled || 'TBD',
            statusClass: statusClass,
            statusText: statusText
        };
    }

    function isMatchClickable(hoverText) {
        // Make all cards clickable, not just live ones
        return true;
    }

    // Simplified countdown timer (if needed)
    function startCountdownTimers() {
        // Add visual effects to live indicators
        const liveIndicators = document.querySelectorAll('.center-status.live');
        liveIndicators.forEach(indicator => {
            indicator.style.animation = 'pulse 1.5s infinite';
        });
    }

    // Tab switching functionality
    function switchTab(filter) {
        // Update active tab
        document.querySelectorAll('.tab-item').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-filter="${filter}"]`).classList.add('active');

        // Use cached data for filtering (no network call)
        currentFilter = filter;
        if (allMatchesData) {
            const filteredData = filterAndGenerateCards(filter);
            showData(filteredData);
        } else {
            // If no cached data, fetch from server
            fetchData(filter);
        }
    }

    // Tab click event listeners
    function initializeTabs() {
        document.querySelectorAll('.tab-item').forEach(tab => {
            tab.addEventListener('click', () => {
                const filter = tab.getAttribute('data-filter');
                switchTab(filter);
            });
        });
    }

    // Initialize
    fetchData();
    initializeTabs();

    console.log('HalwaSport Plugin script loaded - Server-side version');
});

// Global function for match click handling (accessible from inline onclick)
async function halwasportHandleMatchClick(matchUrl, matchId) {
    if (!matchUrl) return;

    const card = document.querySelector(`[data-match-id="${matchId}"]`);
    if (!card) return;

    // Show loading overlay
    showMatchLoading(card);

    try {
        // Extract iframe URL using WordPress AJAX
        const formData = new FormData();
        formData.append('action', 'halwasport_extract_iframe');
        formData.append('url', matchUrl);
        formData.append('nonce', halwasport_ajax.nonce);

        const response = await fetch(halwasport_ajax.ajax_url, {
            method: 'POST',
            body: formData
        });

        if (!response.ok) {
            throw new Error('Network response was not ok');
        }

        const result = await response.json();

        if (result.success && result.iframeUrl) {
            // Open the iframe URL in a new tab
            window.open(result.iframeUrl, '_blank');
            hideMatchLoading(card);
        } else {
            // Show try again later message
            showTryAgainMessage(card);
        }
    } catch (error) {
        console.error('Error extracting iframe:', error);
        showTryAgainMessage(card);
    }
}

// Helper functions for match loading states
function showMatchLoading(card) {
    const overlay = document.createElement('div');
    overlay.className = 'click-loading-overlay';
    overlay.innerHTML = `
        <div class="click-loading-content">
            <div class="click-loader"></div>
            <div class="click-loading-text">Checking stream...</div>
        </div>
    `;
    card.appendChild(overlay);
}

function hideMatchLoading(card) {
    const overlay = card.querySelector('.click-loading-overlay');
    if (overlay) {
        setTimeout(() => overlay.remove(), 2000);
    }
}

function showTryAgainMessage(card) {
    const overlay = card.querySelector('.click-loading-overlay');
    if (overlay) {
        overlay.innerHTML = `
            <div class="click-loading-content try-again">
                <div class="try-again-icon">⏰</div>
                <div class="try-again-title">Try Again Later</div>
                <div class="try-again-text">Stream not available right now</div>
            </div>
        `;
        setTimeout(() => overlay.remove(), 3000);
    }
}
