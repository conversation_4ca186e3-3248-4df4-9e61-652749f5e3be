<?php
/**
 * Plugin Name: HalwaSport
 * Plugin URI: https://halwasport.com
 * Description: Display live sports matches with professional cards and real-time updates. Use shortcode [MATCH_SCORES] to display matches anywhere.
 * Version: 1.0.0
 * Author: HalwaSport
 * Author URI: https://halwasport.com
 * License: GPL v2 or later
 * Text Domain: halwasport
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('HALWASPORT_VERSION', '1.0.1');
define('HALWASPORT_PLUGIN_URL', plugin_dir_url(__FILE__));
define('HALWASPORT_PLUGIN_PATH', plugin_dir_path(__FILE__));

class HalwaSport_Plugin {
    
    public function __construct() {
        add_action('init', array($this, 'init'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_shortcode('MATCH_SCORES', array($this, 'match_scores_shortcode'));
        add_action('wp_ajax_halwasport_get_matches', array($this, 'ajax_get_matches'));
        add_action('wp_ajax_nopriv_halwasport_get_matches', array($this, 'ajax_get_matches'));
        add_action('wp_ajax_halwasport_extract_iframe', array($this, 'ajax_extract_iframe'));
        add_action('wp_ajax_nopriv_halwasport_extract_iframe', array($this, 'ajax_extract_iframe'));
    }
    
    public function init() {
        // Plugin initialization
        load_plugin_textdomain('halwasport', false, dirname(plugin_basename(__FILE__)) . '/languages');
    }
    
    public function enqueue_scripts() {
        // Only enqueue on pages that have the shortcode
        global $post;
        if (is_a($post, 'WP_Post') && has_shortcode($post->post_content, 'MATCH_SCORES')) {
            wp_enqueue_style(
                'halwasport-styles',
                HALWASPORT_PLUGIN_URL . 'assets/css/styles.css',
                array(),
                HALWASPORT_VERSION
            );

            wp_enqueue_script(
                'halwasport-script',
                HALWASPORT_PLUGIN_URL . 'assets/js/plugin-script.js',
                array('jquery'),
                HALWASPORT_VERSION,
                true
            );

            // Localize script for AJAX
            wp_localize_script('halwasport-script', 'halwasport_ajax', array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('halwasport_nonce')
            ));
        }
    }
    
    public function match_scores_shortcode($atts) {
        $atts = shortcode_atts(array(
            'default_tab' => 'today'
        ), $atts, 'MATCH_SCORES');
        
        ob_start();
        ?>
        <div class="halwasport-container">
            <div class="loading-info" id="halwasportLoadingInfo">
                <div class="loader-spinner"></div>
                <p>Loading matches...</p>
            </div>

            <div class="tab-bar" id="halwasportTabBar" style="display: none;">
                <div class="tab-item" data-filter="yesterday">
                    <span class="tab-text">Yesterday</span>
                </div>
                <div class="tab-item active" data-filter="today">
                    <span class="tab-text">Today</span>
                </div>
                <div class="tab-item" data-filter="tomorrow">
                    <span class="tab-text">Tomorrow</span>
                </div>
            </div>

            <div id="halwasportStatus"></div>
            <div id="halwasportDataContainer" class="data-container" style="display: none;">
                <div id="halwasportDataContent" class="data-content"></div>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }
    
    public function ajax_get_matches() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'halwasport_nonce')) {
            wp_die('Security check failed');
        }

        $filter = sanitize_text_field($_POST['filter'] ?? 'today');

        // Include the match processor
        require_once HALWASPORT_PLUGIN_PATH . 'includes/class-match-processor.php';

        $processor = new HalwaSport_Match_Processor();
        $result = $processor->fetchAndProcessMatches($filter);

        wp_send_json($result);
    }
    
    public function ajax_extract_iframe() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'halwasport_nonce')) {
            wp_die('Security check failed');
        }

        $url = esc_url_raw($_POST['url'] ?? '');

        if (empty($url)) {
            wp_send_json(array(
                'success' => false,
                'iframeUrl' => null,
                'error' => 'URL parameter is required'
            ));
        }

        // Include the iframe extractor
        require_once HALWASPORT_PLUGIN_PATH . 'includes/class-iframe-extractor.php';

        $extractor = new HalwaSport_Iframe_Extractor();
        $result = $extractor->extractIframeUrl($url);

        wp_send_json($result);
    }
}

// Initialize the plugin
new HalwaSport_Plugin();

// Activation hook
register_activation_hook(__FILE__, 'halwasport_activate');
function halwasport_activate() {
    // Create any necessary database tables or options
    add_option('halwasport_version', HALWASPORT_VERSION);
}

// Deactivation hook
register_deactivation_hook(__FILE__, 'halwasport_deactivate');
function halwasport_deactivate() {
    // Clean up if necessary
}

// Uninstall hook
register_uninstall_hook(__FILE__, 'halwasport_uninstall');
function halwasport_uninstall() {
    // Remove options and clean up
    delete_option('halwasport_version');
}
