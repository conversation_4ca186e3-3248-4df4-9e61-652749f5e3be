# HalwaSport WordPress Plugin

A professional WordPress plugin for displaying live sports matches with real-time updates and interactive features.

## Features

- **Live Match Display**: Shows live sports matches with professional card design
- **Tab Navigation**: Filter matches by Yesterday, Today, and Tomorrow
- **Real-time Updates**: Live status indicators with animations
- **Responsive Design**: Works perfectly on all devices
- **Click-to-Stream**: Click on live matches to access streams
- **Server-side Processing**: Secure backend processing with protected logic
- **WordPress Integration**: Easy shortcode implementation

## Installation

1. Upload the `halwasport` folder to your `/wp-content/plugins/` directory
2. Activate the plugin through the 'Plugins' menu in WordPress
3. Use the shortcode `[MATCH_SCORES]` anywhere you want to display matches

## Usage

### Basic Shortcode
```
[MATCH_SCORES]
```

### Shortcode with Parameters
```
[MATCH_SCORES default_tab="today"]
```

**Parameters:**
- `default_tab`: Set the default active tab (yesterday, today, tomorrow)

## File Structure

```
halwasport/
├── halwasport.php               # Main plugin file
├── PLUGIN-README.md             # This file
├── assets/
│   ├── css/
│   │   └── styles.css          # Plugin styles
│   └── js/
│       └── plugin-script.js    # Plugin JavaScript
└── includes/
    ├── class-match-processor.php    # Match data processing
    └── class-iframe-extractor.php   # Stream URL extraction
```

## Features in Detail

### Professional Match Cards
- Team logos and names
- Match time and status
- Competition information
- Broadcast details
- Live status indicators

### Tab Navigation
- **Yesterday**: Shows previous day's matches
- **Today**: Shows current day's matches (default)
- **Tomorrow**: Shows upcoming matches

### Interactive Features
- Click on live matches to access streams
- Loading indicators during stream access
- Professional error handling
- Responsive design for all devices

### Security Features
- Server-side data processing
- WordPress nonce verification
- Sanitized inputs and outputs
- Protected API endpoints

## Technical Details

### WordPress Integration
- Uses WordPress AJAX for secure data handling
- Proper enqueueing of scripts and styles
- WordPress coding standards compliance
- Shortcode API integration

### Performance
- Single data fetch with client-side filtering
- Cached data for instant tab switching
- Optimized responsive design
- Minimal server requests

### Browser Support
- Modern browsers (Chrome, Firefox, Safari, Edge)
- Mobile browsers (iOS Safari, Chrome Mobile)
- Responsive design for all screen sizes

## Customization

### Styling
The plugin uses CSS classes that can be customized:
- `.halwasport-container`: Main container
- `.match-card`: Individual match cards
- `.tab-bar`: Tab navigation
- `.team-logo`: Team logo containers

### Colors
The primary color is `#FF6344` and can be changed by modifying the CSS variables.

## Support

For support and customization requests, contact HalwaSport.

## Changelog

### Version 1.0.0
- Initial release
- Live match display functionality
- Tab navigation system
- Responsive design
- Stream access features
- WordPress integration

## License

GPL v2 or later

## Author

**HalwaSport**
- Website: https://halwasport.com
- Plugin URI: https://halwasport.com

---

*This plugin provides a professional solution for displaying sports matches on WordPress websites with advanced features and secure backend processing.*
