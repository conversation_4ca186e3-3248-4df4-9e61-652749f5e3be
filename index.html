<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FawaNews Data Fetcher</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #ffffff;
        }

        .container {
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }







        .data-container {
            margin-top: 20px;
        }

        .data-content {
            word-wrap: break-word;
            font-family: Arial, sans-serif;
            font-size: 14px;
            line-height: 1.4;
        }

        /* Match Cards Styles */
        .matches-container {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }



        .match-card {
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
            padding: 20px;
            border: 1px solid #f0f0f0;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .match-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #ddd, #ddd);
            transition: background 0.3s ease;
        }

        .match-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            border-color: #e0e0e0;
        }

        .match-card:hover::before {
            background: linear-gradient(90deg, #007bff, #0056b3);
        }

        .match-card.status-live::before {
            background: linear-gradient(90deg, #dc3545, #c82333) !important;
        }

        .match-card.status-finished::before {
            background: linear-gradient(90deg, #6c757d, #545b62) !important;
        }

        .match-card.status-coming-soon::before {
            background: linear-gradient(90deg, #ffc107, #e0a800) !important;
        }

        .match-card.status-not-started::before {
            background: linear-gradient(90deg, #28a745, #1e7e34) !important;
        }

        .match-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 123, 255, 0.95);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 10;
        }

        .match-card:hover .match-overlay {
            opacity: 1;
        }

        .overlay-text {
            color: white;
            font-size: 18px;
            font-weight: bold;
            text-align: center;
            padding: 10px;
        }

        .status-live .match-overlay {
            background: rgba(220, 53, 69, 0.95);
        }

        .status-finished .match-overlay {
            background: rgba(108, 117, 125, 0.95);
        }

        .status-coming-soon .match-overlay {
            background: rgba(255, 193, 7, 0.95);
        }

        .status-not-started .match-overlay {
            background: rgba(40, 167, 69, 0.95);
        }

        .match-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .match-time {
            text-align: right;
        }

        .scheduled-time {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }

        .timezone {
            font-size: 11px;
            color: #888;
            margin-top: 2px;
        }

        .match-teams {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
            padding: 15px 0;
        }

        .team {
            display: flex;
            flex-direction: column;
            align-items: center;
            flex: 1;
            max-width: 35%;
        }

        .team-logo {
            width: 70px;
            height: 70px;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8f9fa;
            border-radius: 50%;
            overflow: hidden;
            border: 3px solid #e9ecef;
            transition: transform 0.3s ease;
        }

        .match-card:hover .team-logo {
            transform: scale(1.05);
        }

        .team-logo img {
            width: 55px;
            height: 55px;
            object-fit: contain;
        }

        .team-name {
            font-weight: 600;
            font-size: 15px;
            color: #333;
            text-align: center;
            margin-bottom: 8px;
            line-height: 1.3;
        }

        .team-score {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
            background: #f0f8ff;
            padding: 8px 16px;
            border-radius: 8px;
            min-width: 45px;
            text-align: center;
            border: 2px solid #e3f2fd;
        }

        .match-center {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            flex: 0 0 auto;
            margin: 0 20px;
            min-width: 120px;
        }

        .match-status-center {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            padding: 12px;
            border-radius: 12px;
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            min-height: 80px;
            justify-content: center;
        }

        .status-badge {
            display: flex;
            align-items: center;
            gap: 6px;
            font-weight: bold;
            font-size: 12px;
            padding: 4px 12px;
            border-radius: 20px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            display: inline-block;
        }

        .status-live .status-badge {
            background: #dc3545;
            color: white;
        }

        .status-live .status-indicator {
            background: white;
            animation: pulse 1.5s infinite;
        }

        .status-finished .status-badge {
            background: #6c757d;
            color: white;
        }

        .status-finished .status-indicator {
            background: white;
        }

        .status-coming-soon .status-badge {
            background: #ffc107;
            color: #333;
        }

        .status-coming-soon .status-indicator {
            background: #333;
        }

        .status-not-started .status-badge {
            background: #28a745;
            color: white;
        }

        .status-not-started .status-indicator {
            background: white;
        }

        .countdown-timer {
            font-family: 'Courier New', monospace;
            font-size: 16px;
            font-weight: bold;
            color: #333;
            background: #fff;
            padding: 6px 12px;
            border-radius: 8px;
            border: 1px solid #ddd;
            margin-top: 4px;
            min-width: 80px;
            text-align: center;
        }

        .live-indicator {
            font-family: Arial, sans-serif;
            font-size: 14px;
            font-weight: bold;
            color: #fff;
            background: #dc3545;
            padding: 6px 12px;
            border-radius: 8px;
            margin-top: 4px;
            text-align: center;
            animation: pulse 1.5s infinite;
            text-transform: uppercase;
        }

        .finished-indicator {
            font-family: Arial, sans-serif;
            font-size: 14px;
            font-weight: bold;
            color: #fff;
            background: #6c757d;
            padding: 6px 12px;
            border-radius: 8px;
            margin-top: 4px;
            text-align: center;
            text-transform: uppercase;
        }

        @keyframes pulse {
            0% {
                opacity: 1;
            }

            50% {
                opacity: 0.5;
            }

            100% {
                opacity: 1;
            }
        }

        .match-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 12px;
            border-top: 1px solid #eee;
            margin-bottom: 12px;
        }

        .competition {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
            color: #495057;
            font-size: 14px;
        }

        .broadcast-info {
            display: flex;
            gap: 20px;
        }

        .commentary,
        .tv {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 12px;
            color: #6c757d;
        }



        .no-matches {
            text-align: center;
            padding: 40px;
            color: #666;
            font-size: 18px;
        }

        /* Simple icons using CSS */
        .icon-trophy::before {
            content: "🏆";
            margin-right: 4px;
        }

        .icon-mic::before {
            content: "🎤";
            margin-right: 4px;
        }

        .icon-tv::before {
            content: "📺";
            margin-right: 4px;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .match-teams {
                flex-direction: column;
                gap: 15px;
            }

            .team {
                max-width: 100%;
                flex-direction: row;
                justify-content: space-between;
                width: 100%;
                padding: 10px;
                background: #f8f9fa;
                border-radius: 8px;
            }

            .team-logo {
                width: 40px;
                height: 40px;
                margin-bottom: 0;
            }

            .team-logo img {
                width: 35px;
                height: 35px;
            }

            .team-name {
                margin-bottom: 0;
                flex: 1;
                text-align: left;
                margin-left: 10px;
            }

            .match-vs {
                margin: 10px 0;
            }

            .match-info {
                flex-direction: column;
                gap: 10px;
                align-items: flex-start;
            }

            .broadcast-info {
                flex-direction: column;
                gap: 5px;
            }
        }

        .loading-info {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 60px 20px;
            text-align: center;
        }

        .loader-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }

        .loading-info p {
            color: #666;
            font-size: 16px;
            margin: 0;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        .error-message {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 60px 20px;
            text-align: center;
            color: #666;
        }

        .error-icon {
            font-size: 48px;
            margin-bottom: 20px;
            color: #dc3545;
        }

        .error-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }

        .error-description {
            font-size: 16px;
            color: #666;
            max-width: 400px;
            line-height: 1.5;
        }

        .no-matches {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 60px 20px;
            text-align: center;
            color: #666;
        }

        .no-matches-icon {
            font-size: 48px;
            margin-bottom: 20px;
            color: #6c757d;
        }

        .no-matches-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }

        .no-matches-description {
            font-size: 16px;
            color: #666;
        }
    </style>
</head>

<body>
    <div class="container">


        <div class="loading-info" id="loadingInfo">
            <div class="loader-spinner"></div>
            <p>Loading matches...</p>
        </div>

        <div id="status"></div>
        <div id="dataContainer" class="data-container" style="display: none;">
            <div id="dataContent" class="data-content"></div>
        </div>


    </div>

    <script>
        const loadingInfo = document.getElementById('loadingInfo');
        const statusDiv = document.getElementById('status');
        const dataContainer = document.getElementById('dataContainer');
        const dataContent = document.getElementById('dataContent');

        // Target URL and selector
        const TARGET_URL = 'https://fawanews.co.uk/';
        const TARGET_SELECTOR = '#content > div > div > div > div.nv-content-wrap.entry-content > div.AlbaSportFixture.asp-font';

        // Hover text mapping for clickability
        const HOVER_TEXT_MAPPING = {
            "Soon": "Comming soon",
            "Live": "live",
            "Show": "Watch The Match",
            "NotStarted": "Not started",
            "FinMatch": "Match has finished",
            "Finished": "Finished",
            "NoMatches": "There are no matches Today"
        };

        // Clickable hover texts (Live and Show)
        const CLICKABLE_TEXTS = [
            HOVER_TEXT_MAPPING.Live,      // "live"
            HOVER_TEXT_MAPPING.Show,      // "Watch The Match"
            "live",                       // direct match
            "Watch The Match"             // direct match
        ];

        // CORS proxy options (uncomment one to try)
        const CORS_PROXIES = [
            'https://api.allorigins.win/get?url=',
            'https://cors-anywhere.herokuapp.com/',
            'https://api.codetabs.com/v1/proxy?quest='
        ];

        function showStatus(message, type) {
            // Only show error messages, hide success/loading messages
            if (type === 'error') {
                statusDiv.innerHTML = `
                <div class="error-message">
                    <div class="error-icon">⚠️</div>
                    <div class="error-title">Unable to Load Matches</div>
                    <div class="error-description">${message}</div>
                </div>`;
                statusDiv.style.display = 'block';
            } else {
                statusDiv.style.display = 'none';
            }
        }

        function hideStatus() {
            statusDiv.style.display = 'none';
        }

        function showData(data) {
            dataContent.innerHTML = data;
            dataContainer.style.display = 'block';
            // Start countdown timers after data is displayed
            setTimeout(startCountdownTimers, 100);
        }

        function hideData() {
            dataContainer.style.display = 'none';
        }

        function hideLoading() {
            loadingInfo.style.display = 'none';
        }

        async function fetchWithDirectRequest() {
            try {
                const response = await fetch(TARGET_URL, {
                    method: 'GET',
                    mode: 'cors',
                    headers: {
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                        'User-Agent': 'Mozilla/5.0 (compatible; DataFetcher/1.0)'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const html = await response.text();
                return parseHtmlContent(html);

            } catch (error) {
                console.error('Direct request failed:', error);
                throw new Error(`Direct request failed: ${error.message}`);
            }
        }

        async function fetchWithProxy(proxyUrl) {
            try {
                let requestUrl;

                if (proxyUrl.includes('allorigins')) {
                    requestUrl = `${proxyUrl}${encodeURIComponent(TARGET_URL)}`;
                } else {
                    requestUrl = `${proxyUrl}${TARGET_URL}`;
                }

                const response = await fetch(requestUrl);

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                let html;
                if (proxyUrl.includes('allorigins')) {
                    const data = await response.json();
                    html = data.contents;
                } else {
                    html = await response.text();
                }

                return parseHtmlContent(html);

            } catch (error) {
                console.error(`Proxy request failed (${proxyUrl}):`, error);
                throw new Error(`Proxy request failed: ${error.message}`);
            }
        }

        function parseHtmlContent(html) {
            // Create a temporary DOM parser
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');

            // Try to find the target element
            const targetElement = doc.querySelector(TARGET_SELECTOR);

            if (targetElement) {
                return parseMatchesData(targetElement);
            } else {
                // Try alternative selectors
                const alternatives = [
                    'div.AlbaSportFixture',
                    '.AlbaSportFixture',
                    '[class*="AlbaSportFixture"]'
                ];

                for (const selector of alternatives) {
                    const element = doc.querySelector(selector);
                    if (element) {
                        return parseMatchesData(element);
                    }
                }

                return 'Target element not found with any selector.';
            }
        }

        function parseMatchesData(fixtureElement) {
            const matches = [];

            // Get all match elements
            const matchElements = fixtureElement.querySelectorAll('.AF_Match');

            matchElements.forEach((matchEl, index) => {
                const match = {
                    id: index + 1,
                    status: getMatchStatus(matchEl),
                    startTime: matchEl.getAttribute('data-start') || '',
                    homeTeam: extractTeamData(matchEl, '.AF_FTeam'),
                    awayTeam: extractTeamData(matchEl, '.AF_STeam'),
                    score: extractScore(matchEl),
                    time: extractMatchTime(matchEl),
                    timezone: extractTimezone(matchEl),
                    competition: extractCompetition(matchEl),
                    broadcastInfo: extractBroadcastInfo(matchEl),
                    matchUrl: extractMatchUrl(matchEl),
                    hoverText: extractHoverText(matchEl)
                };

                matches.push(match);
            });

            return createMatchCards(matches);
        }

        function getMatchStatus(matchEl) {
            if (matchEl.classList.contains('finished')) return 'finished';
            if (matchEl.classList.contains('live')) return 'live';
            if (matchEl.classList.contains('comming-soon')) return 'coming-soon';
            if (matchEl.classList.contains('not-started')) return 'not-started';
            return 'unknown';
        }

        function extractTeamData(matchEl, selector) {
            const teamEl = matchEl.querySelector(selector);
            if (!teamEl) return { name: 'Unknown', logo: '' };

            const nameEl = teamEl.querySelector('.AF_TeamName');
            const logoEl = teamEl.querySelector('.AF_TeamLogo img');

            return {
                name: nameEl ? nameEl.textContent.trim() : 'Unknown',
                logo: logoEl ? logoEl.src : ''
            };
        }

        function extractScore(matchEl) {
            const resultElements = matchEl.querySelectorAll('.result');
            if (resultElements.length >= 2) {
                return {
                    home: resultElements[0].textContent.trim(),
                    away: resultElements[1].textContent.trim()
                };
            }
            return { home: '0', away: '0' };
        }

        function extractMatchTime(matchEl) {
            const timeEl = matchEl.querySelector('.AF_EvTime');
            const statusEl = matchEl.querySelector('.AF_StaText');

            return {
                scheduled: timeEl ? timeEl.textContent.trim() : '',
                status: statusEl ? statusEl.textContent.trim() : ''
            };
        }

        function extractTimezone(matchEl) {
            const timezoneEl = matchEl.querySelector('.asp-city');
            return timezoneEl ? timezoneEl.textContent.trim() : 'Unknown';
        }

        function extractCompetition(matchEl) {
            const competitionEl = matchEl.querySelector('.cup');
            return competitionEl ? competitionEl.textContent.trim() : 'Unknown';
        }

        function extractBroadcastInfo(matchEl) {
            const micEl = matchEl.querySelector('.mic');
            const tvEl = matchEl.querySelector('.tv');

            return {
                commentary: micEl ? micEl.textContent.trim() : 'Unknown',
                tv: tvEl ? tvEl.textContent.trim() : 'Unknown'
            };
        }

        function extractMatchUrl(matchEl) {
            const linkEl = matchEl.querySelector('.AF_EventMask');
            return linkEl ? linkEl.href : '';
        }

        function extractHoverText(matchEl) {
            const hoverTextEl = matchEl.querySelector('.AF_MaskText');
            return hoverTextEl ? hoverTextEl.textContent.trim() : '';
        }

        function createMatchCards(matches) {
            if (matches.length === 0) {
                return `
                <div class="no-matches">
                    <div class="no-matches-icon">⚽</div>
                    <div class="no-matches-title">No Matches Available</div>
                    <div class="no-matches-description">There are no matches scheduled at this time.</div>
                </div>`;
            }

            // Store matches data globally for timer updates
            window.matchesData = matches;

            let html = `<div class="matches-container">`;

            matches.forEach(match => {
                const statusClass = `status-${match.status}`;
                const statusText = getStatusDisplayText(match.status, match.time.status);
                const overlayText = getDisplayHoverText(match.hoverText);
                const countdownHtml = getCountdownHtml(match);
                const isClickable = isMatchClickable(match.hoverText);
                const cursorStyle = isClickable ? 'cursor: pointer;' : 'cursor: default; opacity: 0.7;';

                html += `
                <div class="match-card ${statusClass}" data-match-id="${match.id}"
                     onclick="${isClickable ? `handleMatchClick('${match.matchUrl}', ${match.id})` : ''}"
                     style="${cursorStyle}">
                    ${isClickable ? `
                    <div class="match-overlay">
                        <div class="overlay-text">${overlayText}</div>
                    </div>
                    ` : ''}

                    <div class="match-header">
                        <div class="match-time">
                            <div class="scheduled-time">${match.time.scheduled}</div>
                            <div class="timezone">${match.timezone}</div>
                        </div>
                    </div>

                    <div class="match-teams">
                        <div class="team home-team">
                            <div class="team-logo">
                                <img src="${match.homeTeam.logo}" alt="${match.homeTeam.name}" onerror="this.style.display='none'">
                            </div>
                            <div class="team-name">${match.homeTeam.name}</div>
                            <div class="team-score">${match.score.home}</div>
                        </div>

                        <div class="match-center">
                            <div class="match-status-center">
                                <div class="status-badge">
                                    <span class="status-indicator"></span>
                                    ${statusText}
                                </div>
                                ${countdownHtml}
                            </div>
                        </div>

                        <div class="team away-team">
                            <div class="team-score">${match.score.away}</div>
                            <div class="team-name">${match.awayTeam.name}</div>
                            <div class="team-logo">
                                <img src="${match.awayTeam.logo}" alt="${match.awayTeam.name}" onerror="this.style.display='none'">
                            </div>
                        </div>
                    </div>

                    <div class="match-info">
                        <div class="competition">
                            <i class="icon-trophy"></i>
                            ${match.competition}
                        </div>
                        <div class="broadcast-info">
                            <div class="commentary">
                                <i class="icon-mic"></i>
                                ${match.broadcastInfo.commentary}
                            </div>
                            <div class="tv">
                                <i class="icon-tv"></i>
                                ${match.broadcastInfo.tv}
                            </div>
                        </div>
                    </div>
                </div>`;
            });

            html += `</div>`;
            return html;
        }

        function getStatusDisplayText(status, statusText) {
            switch (status) {
                case 'live': return 'LIVE';
                case 'finished': return 'FINISHED';
                case 'coming-soon': return 'COMING SOON';
                case 'not-started': return statusText || 'NOT STARTED';
                default: return statusText || 'UNKNOWN';
            }
        }

        function isMatchClickable(hoverText) {
            if (!hoverText) return false;

            // Check if hover text matches any clickable text (case insensitive)
            return CLICKABLE_TEXTS.some(clickableText =>
                hoverText.toLowerCase() === clickableText.toLowerCase()
            );
        }

        function getDisplayHoverText(hoverText) {
            if (!hoverText) return 'View Match';

            // Return the actual hover text from the source
            return hoverText;
        }



        function getCountdownHtml(match) {
            // Use the hover text to determine what to show in center
            const hoverText = match.hoverText.toLowerCase();

            if (hoverText.includes('live') || hoverText === 'watch the match') {
                return `<div class="live-indicator">LIVE NOW</div>`;
            } else if (hoverText.includes('finished') || hoverText === 'match has finished') {
                return `<div class="finished-indicator">ENDED</div>`;
            } else if (hoverText.includes('not started')) {
                // Show countdown timer for not started matches
                return `<div class="countdown-timer" id="timer-${match.id}">Starting Soon</div>`;
            } else if (hoverText.includes('comming soon')) {
                return `<div class="countdown-timer" id="timer-${match.id}">Coming Soon</div>`;
            }

            return `<div class="countdown-timer">${match.hoverText}</div>`;
        }

        function handleMatchClick(url, matchId) {
            const match = window.matchesData?.find(m => m.id == matchId);
            if (!match) return;

            // Only allow clicking if hover text indicates it's clickable
            if (!isMatchClickable(match.hoverText)) {
                return;
            }

            if (url) {
                window.open(url, '_blank');
            }
        }

        function startCountdownTimers() {
            // Add visual effects to timer elements
            const timers = document.querySelectorAll('.countdown-timer');
            timers.forEach(timer => {
                timer.style.animation = 'pulse 2s infinite';
            });

            const liveIndicators = document.querySelectorAll('.live-indicator');
            liveIndicators.forEach(indicator => {
                indicator.style.animation = 'pulse 1.5s infinite';
            });
        }

        async function fetchData() {
            hideData();

            try {
                // Try direct request first
                try {
                    const data = await fetchWithDirectRequest();
                    hideLoading();
                    showData(data);
                    return;
                } catch (directError) {
                    console.log('Direct request failed, trying proxies...');
                }

                // Try each proxy
                for (const proxy of CORS_PROXIES) {
                    try {
                        const data = await fetchWithProxy(proxy);
                        hideLoading();
                        showData(data);
                        return;
                    } catch (proxyError) {
                        console.log(`Proxy ${proxy} failed:`, proxyError);
                        continue;
                    }
                }

                // If all methods fail
                throw new Error('Please check your internet connection and try refreshing the page.');

            } catch (error) {
                hideLoading();
                showStatus(error.message, 'error');
                console.error('Fetch error:', error);
            }
        }

        // Auto-fetch data when page loads
        window.addEventListener('load', fetchData);

        // Auto-hide status after 10 seconds for non-error messages
        function autoHideStatus() {
            setTimeout(() => {
                if (statusDiv.classList.contains('success') || statusDiv.classList.contains('loading')) {
                    hideStatus();
                }
            }, 10000);
        }

        // Override showStatus to include auto-hide
        const originalShowStatus = showStatus;
        showStatus = function (message, type) {
            originalShowStatus(message, type);
            if (type === 'success' || type === 'loading') {
                autoHideStatus();
            }
        };

        console.log('FawaNews Data Fetcher initialized');
        console.log('Target URL:', TARGET_URL);
        console.log('Target Selector:', TARGET_SELECTOR);
    </script>
</body>

</html>