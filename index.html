<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FawaNews Data Fetcher</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }

        .fetch-button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-bottom: 20px;
            transition: background-color 0.3s;
        }

        .fetch-button:hover {
            background-color: #0056b3;
        }

        .fetch-button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }

        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }

        .status.loading {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .data-container {
            margin-top: 20px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }

        .data-content {
            white-space: pre-wrap;
            word-wrap: break-word;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.4;
            max-height: 600px;
            overflow-y: auto;
            background-color: #ffffff;
            padding: 15px;
            border: 1px solid #ccc;
            border-radius: 3px;
        }

        .cors-info {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }

        .cors-info h3 {
            margin-top: 0;
            color: #0066cc;
        }

        .alternative-methods {
            margin-top: 30px;
            padding: 20px;
            background-color: #f0f8ff;
            border-radius: 5px;
            border: 1px solid #cce7ff;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>FawaNews Div Data Extractor</h1>

        <div class="cors-info">
            <h3>⚠️ CORS Notice</h3>
            <p>Due to browser security policies (CORS), direct requests to external websites are typically blocked.
                This page will attempt to fetch data, but it may fail due to CORS restrictions.</p>
        </div>

        <button id="fetchButton" class="fetch-button">Extract All Div Data from FawaNews</button>

        <div id="status"></div>
        <div id="dataContainer" class="data-container" style="display: none;">
            <h3>Extracted Data:</h3>
            <div id="dataContent" class="data-content"></div>
        </div>

        <div class="alternative-methods">
            <h3>Alternative Methods:</h3>
            <ul>
                <li><strong>CORS Proxy:</strong> Use a CORS proxy service like cors-anywhere or allorigins</li>
                <li><strong>Browser Extension:</strong> Install a CORS-disabling browser extension for development</li>
                <li><strong>Server-side Proxy:</strong> Create a backend service to fetch the data</li>
                <li><strong>Browser Dev Mode:</strong> Launch Chrome with --disable-web-security flag</li>
            </ul>
        </div>
    </div>

    <script>
        const fetchButton = document.getElementById('fetchButton');
        const statusDiv = document.getElementById('status');
        const dataContainer = document.getElementById('dataContainer');
        const dataContent = document.getElementById('dataContent');

        // Target URL and selector
        const TARGET_URL = 'https://fawanews.co.uk/';
        const TARGET_SELECTOR = '#content > div > div > div > div.nv-content-wrap.entry-content > div.AlbaSportFixture.asp-font';

        // CORS proxy options (uncomment one to try)
        const CORS_PROXIES = [
            'https://api.allorigins.win/get?url=',
            'https://cors-anywhere.herokuapp.com/',
            'https://api.codetabs.com/v1/proxy?quest='
        ];

        function showStatus(message, type) {
            statusDiv.innerHTML = message;
            statusDiv.className = `status ${type}`;
            statusDiv.style.display = 'block';
        }

        function hideStatus() {
            statusDiv.style.display = 'none';
        }

        function showData(data) {
            dataContent.textContent = data;
            dataContainer.style.display = 'block';
        }

        function hideData() {
            dataContainer.style.display = 'none';
        }

        async function fetchWithDirectRequest() {
            showStatus('Attempting direct request...', 'loading');

            try {
                const response = await fetch(TARGET_URL, {
                    method: 'GET',
                    mode: 'cors',
                    headers: {
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                        'User-Agent': 'Mozilla/5.0 (compatible; DataFetcher/1.0)'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const html = await response.text();
                return parseHtmlContent(html);

            } catch (error) {
                console.error('Direct request failed:', error);
                throw new Error(`Direct request failed: ${error.message}`);
            }
        }

        async function fetchWithProxy(proxyUrl) {
            showStatus(`Trying proxy: ${proxyUrl}...`, 'loading');

            try {
                let requestUrl;

                if (proxyUrl.includes('allorigins')) {
                    requestUrl = `${proxyUrl}${encodeURIComponent(TARGET_URL)}`;
                } else {
                    requestUrl = `${proxyUrl}${TARGET_URL}`;
                }

                const response = await fetch(requestUrl);

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                let html;
                if (proxyUrl.includes('allorigins')) {
                    const data = await response.json();
                    html = data.contents;
                } else {
                    html = await response.text();
                }

                return parseHtmlContent(html);

            } catch (error) {
                console.error(`Proxy request failed (${proxyUrl}):`, error);
                throw new Error(`Proxy request failed: ${error.message}`);
            }
        }

        function parseHtmlContent(html) {
            // Create a temporary DOM parser
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');

            let result = '';

            // Try to find the target element
            const targetElement = doc.querySelector(TARGET_SELECTOR);

            if (targetElement) {
                result += `=== MAIN TARGET ELEMENT FOUND ===\n`;
                result += `Selector: ${TARGET_SELECTOR}\n`;
                result += `Tag: ${targetElement.tagName}\n`;
                result += `Classes: ${targetElement.className}\n`;
                result += `Text Content: ${targetElement.textContent.trim()}\n`;
                result += `Inner HTML: ${targetElement.innerHTML}\n\n`;

                // Get all child divs within the target element
                const childDivs = targetElement.querySelectorAll('div');
                result += `=== CHILD DIVS (${childDivs.length} found) ===\n`;

                childDivs.forEach((div, index) => {
                    result += `--- Div ${index + 1} ---\n`;
                    result += `Tag: ${div.tagName}\n`;
                    result += `Classes: ${div.className || 'No classes'}\n`;
                    result += `ID: ${div.id || 'No ID'}\n`;
                    result += `Text Content: ${div.textContent.trim() || 'No text content'}\n`;
                    result += `Inner HTML: ${div.innerHTML || 'No inner HTML'}\n`;
                    result += `Attributes: ${Array.from(div.attributes).map(attr => `${attr.name}="${attr.value}"`).join(', ') || 'No attributes'}\n\n`;
                });

                return result;
            } else {
                // If exact selector doesn't work, try to find similar elements
                result += `=== TARGET ELEMENT NOT FOUND ===\n`;
                result += `Tried selector: ${TARGET_SELECTOR}\n\n`;

                const alternatives = [
                    'div.AlbaSportFixture',
                    '.AlbaSportFixture',
                    '[class*="AlbaSportFixture"]',
                    '[class*="asp-font"]',
                    '#content',
                    '.nv-content-wrap',
                    '.entry-content'
                ];

                result += `=== TRYING ALTERNATIVE SELECTORS ===\n`;

                for (const selector of alternatives) {
                    const elements = doc.querySelectorAll(selector);
                    if (elements.length > 0) {
                        result += `--- Found ${elements.length} element(s) with selector: ${selector} ---\n`;

                        elements.forEach((element, index) => {
                            result += `Element ${index + 1}:\n`;
                            result += `Tag: ${element.tagName}\n`;
                            result += `Classes: ${element.className || 'No classes'}\n`;
                            result += `ID: ${element.id || 'No ID'}\n`;
                            result += `Text Content: ${element.textContent.trim().substring(0, 200)}${element.textContent.trim().length > 200 ? '...' : ''}\n`;

                            // Get all divs within this element
                            const divs = element.querySelectorAll('div');
                            result += `Child divs: ${divs.length}\n`;

                            if (divs.length > 0 && divs.length <= 10) {
                                divs.forEach((div, divIndex) => {
                                    result += `  Div ${divIndex + 1}: ${div.className || 'no-class'} - ${div.textContent.trim().substring(0, 100)}${div.textContent.trim().length > 100 ? '...' : ''}\n`;
                                });
                            }
                            result += `\n`;
                        });
                    }
                }

                if (result.includes('Found')) {
                    return result;
                } else {
                    result += `No elements found with any alternative selector.\n`;
                    result += `Available elements in document:\n`;

                    // Show some general page structure
                    const allDivs = doc.querySelectorAll('div');
                    result += `Total divs in document: ${allDivs.length}\n`;

                    // Show first 10 divs with classes
                    const divsWithClasses = Array.from(allDivs).filter(div => div.className).slice(0, 10);
                    result += `First 10 divs with classes:\n`;
                    divsWithClasses.forEach((div, index) => {
                        result += `${index + 1}. ${div.tagName}.${div.className} - ${div.textContent.trim().substring(0, 50)}${div.textContent.trim().length > 50 ? '...' : ''}\n`;
                    });

                    return result;
                }
            }
        }

        async function fetchData() {
            fetchButton.disabled = true;
            hideData();

            try {
                // Try direct request first
                try {
                    const data = await fetchWithDirectRequest();
                    showStatus('✅ Data fetched successfully via direct request!', 'success');
                    showData(data);
                    return;
                } catch (directError) {
                    console.log('Direct request failed, trying proxies...');
                }

                // Try each proxy
                for (const proxy of CORS_PROXIES) {
                    try {
                        const data = await fetchWithProxy(proxy);
                        showStatus(`✅ Data fetched successfully via proxy: ${proxy}`, 'success');
                        showData(data);
                        return;
                    } catch (proxyError) {
                        console.log(`Proxy ${proxy} failed:`, proxyError);
                        continue;
                    }
                }

                // If all methods fail
                throw new Error('All fetch methods failed. Please try alternative approaches.');

            } catch (error) {
                showStatus(`❌ Error: ${error.message}`, 'error');
                console.error('Fetch error:', error);
            } finally {
                fetchButton.disabled = false;
            }
        }

        // Event listeners
        fetchButton.addEventListener('click', fetchData);

        // Auto-hide status after 10 seconds for non-error messages
        function autoHideStatus() {
            setTimeout(() => {
                if (statusDiv.classList.contains('success') || statusDiv.classList.contains('loading')) {
                    hideStatus();
                }
            }, 10000);
        }

        // Override showStatus to include auto-hide
        const originalShowStatus = showStatus;
        showStatus = function (message, type) {
            originalShowStatus(message, type);
            if (type === 'success' || type === 'loading') {
                autoHideStatus();
            }
        };

        console.log('FawaNews Data Fetcher initialized');
        console.log('Target URL:', TARGET_URL);
        console.log('Target Selector:', TARGET_SELECTOR);
    </script>
</body>

</html>