<?php
/**
 * Iframe Extractor Class
 *
 * @package HalwaSport
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class HalwaSport_Iframe_Extractor {
    private $corsProxies = [
        'https://api.allorigins.win/get?url=',
        'https://cors-anywhere.herokuapp.com/',
        'https://api.codetabs.com/v1/proxy?quest='
    ];
    
    public function extractIframeUrl($url) {
        try {
            $html = $this->fetchPageContent($url);
            
            if (!$html) {
                return ['success' => false, 'iframeUrl' => null];
            }
            
            $iframeUrl = $this->parseIframeFromHtml($html, $url);
            
            return [
                'success' => true,
                'iframeUrl' => $iframeUrl
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'iframeUrl' => null,
                'error' => $e->getMessage()
            ];
        }
    }
    
    private function fetchPageContent($url) {
        // Try direct request first
        $html = $this->fetchDirect($url);
        if ($html) return $html;
        
        // Try proxies
        foreach ($this->corsProxies as $proxy) {
            $html = $this->fetchWithProxy($url, $proxy);
            if ($html) return $html;
        }
        
        return false;
    }
    
    private function fetchDirect($url) {
        $context = stream_context_create([
            'http' => [
                'method' => 'GET',
                'header' => [
                    'User-Agent: Mozilla/5.0 (compatible; IframeExtractor/1.0)',
                    'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
                ],
                'timeout' => 30
            ]
        ]);
        
        return @file_get_contents($url, false, $context);
    }
    
    private function fetchWithProxy($url, $proxy) {
        try {
            if (strpos($proxy, 'allorigins') !== false) {
                $proxyUrl = $proxy . urlencode($url);
                $response = @file_get_contents($proxyUrl);
                if ($response) {
                    $data = json_decode($response, true);
                    return $data['contents'] ?? false;
                }
            } else {
                $proxyUrl = $proxy . $url;
                return @file_get_contents($proxyUrl);
            }
        } catch (Exception $e) {
            return false;
        }
        
        return false;
    }
    
    private function parseIframeFromHtml($html, $originalUrl) {
        $dom = new DOMDocument();
        @$dom->loadHTML($html);
        
        $iframes = $dom->getElementsByTagName('iframe');
        
        if ($iframes->length === 0) {
            return null;
        }
        
        $iframe = $iframes->item(0);
        $iframeSrc = $iframe->getAttribute('src');
        
        if (!$iframeSrc) {
            return null;
        }
        
        return $this->normalizeIframeUrl($iframeSrc, $originalUrl);
    }
    
    private function normalizeIframeUrl($iframeSrc, $originalUrl) {
        // Clean up malformed URLs
        if (strpos($iframeSrc, 'file:/') !== false) {
            $fileIndex = strpos($iframeSrc, 'file:/');
            $iframeSrc = substr($iframeSrc, $fileIndex + 6);
            
            if (!preg_match('/^https?:\/\//', $iframeSrc)) {
                $iframeSrc = 'https://' . $iframeSrc;
            }
        }
        
        // Handle different URL formats
        if (preg_match('/^https?:\/\//', $iframeSrc)) {
            // Already absolute URL
            return $iframeSrc;
        } elseif (strpos($iframeSrc, '//') === 0) {
            // Protocol-relative URL
            return 'https:' . $iframeSrc;
        } elseif (strpos($iframeSrc, '/') === 0) {
            // Root-relative URL
            $parsedUrl = parse_url($originalUrl);
            return $parsedUrl['scheme'] . '://' . $parsedUrl['host'] . $iframeSrc;
        } elseif ($iframeSrc) {
            // Relative URL or domain-only
            if (strpos($iframeSrc, '.') !== false && strpos($iframeSrc, '/') !== 0) {
                return 'https://' . $iframeSrc;
            } else {
                $parsedUrl = parse_url($originalUrl);
                return $parsedUrl['scheme'] . '://' . $parsedUrl['host'] . '/' . $iframeSrc;
            }
        }
        
        return null;
    }
}
