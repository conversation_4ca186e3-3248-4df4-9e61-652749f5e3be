<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

class MatchDataProcessor {
    private $targetUrl = 'https://fawanews.co.uk/';
    private $targetSelector = '#content > div > div > div > div.nv-content-wrap.entry-content > div.AlbaSportFixture.asp-font';
    
    private $corsProxies = [
        'https://api.allorigins.win/get?url=',
        'https://cors-anywhere.herokuapp.com/',
        'https://api.codetabs.com/v1/proxy?quest='
    ];
    
    private $hoverTextMapping = [
        "Soon" => "Comming soon",
        "Live" => "live", 
        "Show" => "Watch The Match",
        "NotStarted" => "Not started",
        "FinMatch" => "Match has finished",
        "Finished" => "Finished",
        "NoMatches" => "There are no matches Today"
    ];
    
    private $clickableTexts = ["live", "Watch The Match"];
    
    public function fetchAndProcessMatches($filter = 'today') {
        try {
            $html = $this->fetchHtmlContent();
            if (!$html) {
                return $this->createErrorResponse('Unable to fetch data');
            }

            $allMatches = $this->parseHtmlContent($html);

            // Always return all matches data for client-side filtering
            return [
                'success' => true,
                'data' => $this->generateMatchCards($allMatches, $filter),
                'allMatches' => $allMatches
            ];

        } catch (Exception $e) {
            return $this->createErrorResponse($e->getMessage());
        }
    }
    
    private function fetchHtmlContent() {
        // Try direct request first
        $html = $this->fetchDirect();
        if ($html) return $html;
        
        // Try proxies
        foreach ($this->corsProxies as $proxy) {
            $html = $this->fetchWithProxy($proxy);
            if ($html) return $html;
        }
        
        return false;
    }
    
    private function fetchDirect() {
        $context = stream_context_create([
            'http' => [
                'method' => 'GET',
                'header' => [
                    'User-Agent: Mozilla/5.0 (compatible; DataFetcher/1.0)',
                    'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
                ],
                'timeout' => 30
            ]
        ]);
        
        return @file_get_contents($this->targetUrl, false, $context);
    }
    
    private function fetchWithProxy($proxy) {
        try {
            if (strpos($proxy, 'allorigins') !== false) {
                $url = $proxy . urlencode($this->targetUrl);
                $response = @file_get_contents($url);
                if ($response) {
                    $data = json_decode($response, true);
                    return $data['contents'] ?? false;
                }
            } else {
                $url = $proxy . $this->targetUrl;
                return @file_get_contents($url);
            }
        } catch (Exception $e) {
            return false;
        }
        
        return false;
    }
    
    private function parseHtmlContent($html) {
        try {
            $dom = new DOMDocument();
            @$dom->loadHTML($html);
            $xpath = new DOMXPath($dom);

            // Find the main fixture element
            $fixtureElements = $xpath->query("//div[contains(@class, 'AlbaSportFixture')]");

            if ($fixtureElements === false || $fixtureElements->length === 0) {
                return [];
            }

            $fixtureElement = $fixtureElements->item(0);
            return $this->parseMatchesData($fixtureElement, $xpath);
        } catch (Exception $e) {
            error_log("Error parsing HTML content: " . $e->getMessage());
            return [];
        }
    }
    
    private function parseMatchesData($fixtureElement, $xpath) {
        $allMatches = [
            'yesterday' => [],
            'today' => [],
            'tomorrow' => []
        ];
        
        $globalId = 1;
        
        // Parse matches from each day's div
        $days = ['yesterday', 'today', 'tomorrow'];
        
        foreach ($days as $day) {
            $dayDiv = $xpath->query(".//div[@id='aspwp-{$day}']", $fixtureElement);

            if ($dayDiv !== false && $dayDiv->length > 0) {
                $matchElements = $xpath->query(".//div[contains(@class, 'AF_Match')]", $dayDiv->item(0));

                if ($matchElements !== false) {
                    foreach ($matchElements as $matchEl) {
                        try {
                            $match = $this->createMatchObject($matchEl, $xpath, $globalId++, $day);
                            $allMatches[$day][] = $match;
                        } catch (Exception $e) {
                            error_log("Error creating match object: " . $e->getMessage());
                            continue;
                        }
                    }
                }
            }
        }
        
        return $allMatches;
    }
    
    private function createMatchObject($matchEl, $xpath, $id, $day) {
        return [
            'id' => $id,
            'day' => $day,
            'status' => $this->getMatchStatus($matchEl),
            'startTime' => $matchEl->getAttribute('data-start') ?: '',
            'homeTeam' => $this->extractTeamData($matchEl, $xpath, '.AF_FTeam'),
            'awayTeam' => $this->extractTeamData($matchEl, $xpath, '.AF_STeam'),
            'time' => $this->extractMatchTime($matchEl, $xpath),
            'timezone' => $this->extractTimezone($matchEl, $xpath),
            'competition' => $this->extractCompetition($matchEl, $xpath),
            'broadcastInfo' => $this->extractBroadcastInfo($matchEl, $xpath),
            'matchUrl' => $this->extractMatchUrl($matchEl, $xpath),
            'hoverText' => $this->extractHoverText($matchEl, $xpath)
        ];
    }
    
    private function getMatchStatus($matchEl) {
        $classes = $matchEl->getAttribute('class');
        if (strpos($classes, 'finished') !== false) return 'finished';
        if (strpos($classes, 'live') !== false) return 'live';
        if (strpos($classes, 'comming-soon') !== false) return 'coming-soon';
        if (strpos($classes, 'not-started') !== false) return 'not-started';
        return 'unknown';
    }
    
    private function extractTeamData($matchEl, $xpath, $selector) {
        // Fix XPath selector - remove the dot from CSS class selector
        $classSelector = str_replace('.', '', $selector);
        $teamElements = $xpath->query(".//div[contains(@class, '{$classSelector}')]", $matchEl);

        if ($teamElements === false || $teamElements->length === 0) {
            return ['name' => 'Unknown', 'logo' => ''];
        }

        $teamEl = $teamElements->item(0);
        $nameElements = $xpath->query(".//div[contains(@class, 'AF_TeamName')]", $teamEl);
        $logoElements = $xpath->query(".//div[contains(@class, 'AF_TeamLogo')]//img", $teamEl);

        return [
            'name' => ($nameElements !== false && $nameElements->length > 0) ? trim($nameElements->item(0)->textContent) : 'Unknown',
            'logo' => ($logoElements !== false && $logoElements->length > 0) ? $logoElements->item(0)->getAttribute('src') : ''
        ];
    }
    
    private function extractMatchTime($matchEl, $xpath) {
        $timeElements = $xpath->query(".//div[contains(@class, 'AF_EvTime')]", $matchEl);
        $statusElements = $xpath->query(".//div[contains(@class, 'AF_StaText')]", $matchEl);

        return [
            'scheduled' => ($timeElements !== false && $timeElements->length > 0) ? trim($timeElements->item(0)->textContent) : '',
            'status' => ($statusElements !== false && $statusElements->length > 0) ? trim($statusElements->item(0)->textContent) : ''
        ];
    }
    
    private function extractTimezone($matchEl, $xpath) {
        $timezoneElements = $xpath->query(".//div[contains(@class, 'asp-city')]", $matchEl);
        return ($timezoneElements !== false && $timezoneElements->length > 0) ? trim($timezoneElements->item(0)->textContent) : 'Unknown';
    }
    
    private function extractCompetition($matchEl, $xpath) {
        // Look for competition in the AF_Footer section specifically
        $searches = [
            ".//div[@class='AF_Footer']//span[@class='cup asp-flex']",
            ".//div[contains(@class, 'AF_Footer')]//span[contains(@class, 'cup')]",
            ".//div[contains(@class, 'AF_EvInfo')]//span[contains(@class, 'cup')]",
            ".//span[@class='cup asp-flex']",
            ".//span[contains(@class, 'cup')]",
            ".//div[contains(@class, 'cup')]"
        ];

        foreach ($searches as $query) {
            $elements = $xpath->query($query, $matchEl);
            if ($elements !== false && $elements->length > 0) {
                $text = trim($elements->item(0)->textContent);
                if (!empty($text) && $text !== 'Unknown') {
                    return $text;
                }
            }
        }

        return 'Unknown';
    }
    
    private function extractBroadcastInfo($matchEl, $xpath) {
        // Look for broadcast info in the AF_Footer section specifically
        $micElements = $xpath->query(".//div[contains(@class, 'AF_Footer')]//span[contains(@class, 'mic')]", $matchEl);
        $tvElements = $xpath->query(".//div[contains(@class, 'AF_Footer')]//span[contains(@class, 'tv')]", $matchEl);

        // Fallback to any mic/tv elements
        if ($micElements === false || $micElements->length === 0) {
            $micElements = $xpath->query(".//span[contains(@class, 'mic')]", $matchEl);
        }
        if ($tvElements === false || $tvElements->length === 0) {
            $tvElements = $xpath->query(".//span[contains(@class, 'tv')]", $matchEl);
        }

        // Final fallback to div elements
        if ($micElements === false || $micElements->length === 0) {
            $micElements = $xpath->query(".//div[contains(@class, 'mic')]", $matchEl);
        }
        if ($tvElements === false || $tvElements->length === 0) {
            $tvElements = $xpath->query(".//div[contains(@class, 'tv')]", $matchEl);
        }

        return [
            'commentary' => ($micElements !== false && $micElements->length > 0) ? trim($micElements->item(0)->textContent) : 'Unknown',
            'tv' => ($tvElements !== false && $tvElements->length > 0) ? trim($tvElements->item(0)->textContent) : 'Unknown'
        ];
    }
    
    private function extractMatchUrl($matchEl, $xpath) {
        $linkElements = $xpath->query(".//a[contains(@class, 'AF_EventMask')]", $matchEl);
        return ($linkElements !== false && $linkElements->length > 0) ? $linkElements->item(0)->getAttribute('href') : '';
    }

    private function extractHoverText($matchEl, $xpath) {
        $hoverElements = $xpath->query(".//div[contains(@class, 'AF_MaskText')]", $matchEl);
        return ($hoverElements !== false && $hoverElements->length > 0) ? trim($hoverElements->item(0)->textContent) : '';
    }
    
    private function filterMatchesByDay($allMatches, $filter) {
        switch ($filter) {
            case 'yesterday':
                return $allMatches['yesterday'] ?? [];
            case 'today':
                return $allMatches['today'] ?? [];
            case 'tomorrow':
                return $allMatches['tomorrow'] ?? [];
            default:
                return $allMatches['today'] ?? [];
        }
    }
    
    private function generateMatchCards($allMatches, $filter) {
        // Get matches for the specific filter
        $filteredMatches = $this->filterMatchesByDay($allMatches, $filter);

        if (empty($filteredMatches)) {
            return $this->createEmptyStateMessage($filter);
        }

        $sortedMatches = $this->sortMatchesByPriority($filteredMatches);

        $html = '<div class="matches-container">';

        foreach ($sortedMatches as $match) {
            $html .= $this->createMatchCard($match);
        }

        $html .= '</div>';

        return $html;
    }
    
    private function sortMatchesByPriority($matches) {
        usort($matches, function($a, $b) {
            $priorityA = $this->getMatchPriority($a['hoverText']);
            $priorityB = $this->getMatchPriority($b['hoverText']);
            
            if ($priorityA !== $priorityB) {
                return $priorityA - $priorityB;
            }
            
            return strcmp($a['time']['scheduled'], $b['time']['scheduled']);
        });
        
        return $matches;
    }
    
    private function getMatchPriority($hoverText) {
        $text = strtolower($hoverText);
        
        if (strpos($text, 'live') !== false || $text === 'watch the match') {
            return 1; // Highest priority - Live matches
        } elseif (strpos($text, 'comming soon') !== false) {
            return 2; // Coming soon matches
        } elseif (strpos($text, 'not started') !== false) {
            return 3; // Not started matches
        } elseif (strpos($text, 'finished') !== false || $text === 'match has finished') {
            return 4; // Lowest priority - Finished matches
        }
        
        return 4; // Default to lowest priority
    }

    private function createMatchCard($match) {
        $statusClass = "status-{$match['status']}";
        $overlayText = $this->getDisplayHoverText($match['hoverText']);
        $centerContent = $this->getCenterContent($match);
        $isClickable = $this->isMatchClickable($match['hoverText']);
        $cursorStyle = $isClickable ? 'cursor: pointer;' : 'cursor: default;';

        $overlayHtml = '';
        if ($isClickable) {
            $overlayHtml = "
            <div class=\"match-overlay\">
                <div class=\"overlay-text\">{$overlayText}</div>
            </div>";
        }

        $clickHandler = $isClickable ? "onclick=\"handleMatchClick('{$match['matchUrl']}', {$match['id']})\"" : '';

        return "
        <div class=\"match-card {$statusClass}\" data-match-id=\"{$match['id']}\"
             {$clickHandler} style=\"{$cursorStyle}\">
            {$overlayHtml}

            <div class=\"match-teams\">
                <div class=\"team home-team\">
                    <div class=\"team-logo\">
                        <img src=\"{$match['homeTeam']['logo']}\" alt=\"{$match['homeTeam']['name']}\" onerror=\"this.style.display='none'\">
                    </div>
                    <div class=\"team-name\">{$match['homeTeam']['name']}</div>
                </div>

                <div class=\"match-center\">
                    <div class=\"match-time-center\">
                        <div class=\"center-time\">{$centerContent['time']}</div>
                        <div class=\"center-status {$centerContent['statusClass']}\">{$centerContent['statusText']}</div>
                    </div>
                </div>

                <div class=\"team away-team\">
                    <div class=\"team-logo\">
                        <img src=\"{$match['awayTeam']['logo']}\" alt=\"{$match['awayTeam']['name']}\" onerror=\"this.style.display='none'\">
                    </div>
                    <div class=\"team-name\">{$match['awayTeam']['name']}</div>
                </div>
            </div>

            <div class=\"match-info\">
                <div class=\"competition\">
                    <i class=\"icon-trophy\"></i>
                    {$match['competition']}
                </div>
            </div>
        </div>";
    }

    private function getCenterContent($match) {
        $hoverText = strtolower($match['hoverText']);
        $statusClass = '';
        $statusText = '';

        if (strpos($hoverText, 'live') !== false || $hoverText === 'watch the match') {
            $statusClass = 'live';
            $statusText = 'Live';
        } elseif (strpos($hoverText, 'finished') !== false || $hoverText === 'match has finished') {
            $statusClass = 'finished';
            $statusText = 'Match Finished';
        } elseif (strpos($hoverText, 'not started') !== false || strpos($hoverText, 'comming soon') !== false) {
            $statusClass = 'upcoming';
            $statusText = 'Upcoming';
        } else {
            $statusClass = 'upcoming';
            $statusText = 'Upcoming';
        }

        return [
            'time' => $match['time']['scheduled'] ?: 'TBD',
            'statusClass' => $statusClass,
            'statusText' => $statusText
        ];
    }

    private function isMatchClickable($hoverText) {
        // Make all cards clickable if they have hover text
        return !empty($hoverText);
    }

    private function getDisplayHoverText($hoverText) {
        return $hoverText ?: 'View Match';
    }

    private function createEmptyStateMessage($filter) {
        $messages = [
            'today' => [
                'icon' => '📅',
                'title' => 'No Events Today',
                'description' => 'There are no events scheduled for today. Check other days for upcoming events!'
            ],
            'yesterday' => [
                'icon' => '⏮️',
                'title' => 'No Events Yesterday',
                'description' => 'There were no events yesterday. Check today\'s or tomorrow\'s schedule!'
            ],
            'tomorrow' => [
                'icon' => '⏭️',
                'title' => 'No Events Tomorrow',
                'description' => 'There are no events scheduled for tomorrow yet. Check back later for updates!'
            ]
        ];

        $message = $messages[$filter] ?? $messages['today'];

        return "
        <div class=\"no-matches\">
            <div class=\"no-matches-icon\">{$message['icon']}</div>
            <div class=\"no-matches-title\">{$message['title']}</div>
            <div class=\"no-matches-description\">{$message['description']}</div>
        </div>";
    }

    private function createErrorResponse($message) {
        return [
            'success' => false,
            'data' => "
            <div class=\"no-matches\">
                <div class=\"no-matches-icon\">📋</div>
                <div class=\"no-matches-title\">No Events Available</div>
                <div class=\"no-matches-description\">There are no events scheduled at the moment. Please check back later.</div>
            </div>"
        ];
    }
}

// Handle the request
$filter = $_GET['filter'] ?? 'today';
$processor = new MatchDataProcessor();
$result = $processor->fetchAndProcessMatches($filter);

echo json_encode($result);
